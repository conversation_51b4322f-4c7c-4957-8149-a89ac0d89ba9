@import url('https://fonts.googleapis.com/css2?family=Assistant:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:wght@400;500&display=swap');

@import 'tailwindcss/base';
@import '../../../../deps/petal_components/assets/default.css';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .btn {
    @apply box-border flex justify-center items-center py-2 px-4 space-x-2 min-h-[46px] text-base font-semibold tracking-[0.2px] !leading-tight disabled:text-text-disabled truncate rounded-lg outline-none transition-shadow disabled:cursor-not-allowed;
  }

  .btn-primary-black {
    @apply text-white bg-primary-off-black disabled:bg-secondary-grey-light hover:shadow-lg disabled:hover:shadow-none focus:shadow-button-focused disabled:focus:shadow-none disabled:active:shadow-none active:shadow-button-black-pressed dark:bg-gray-600 dark:text-white;
  }

  .btn-primary-white {
    @apply text-text-main bg-white disabled:bg-secondary-grey-light shadow hover:shadow-lg disabled:hover:shadow-none disabled:active:shadow-none active:shadow-button-white-pressed disabled:shadow-none dark:bg-primary-off-black dark:text-white;
  }

  .btn-secondary {
    @apply text-text-main bg-white border border-primary-off-black disabled:border-secondary-grey-light shadow hover:shadow-md disabled:hover:shadow-none active:shadow-none disabled:shadow-none dark:bg-primary-off-black dark:text-white dark:border-white;
  }

  .btn-tertiary {
    @apply text-text-main underline bg-white hover:bg-primary-green-dark/10 disabled:hover:bg-white active:bg-white dark:text-white dark:bg-primary-off-black dark:hover:bg-primary-green-dark/30;
  }

  .checkbox {
    @apply w-6 h-6 border border-gray-500 rounded-sm focus:ring-0 focus:ring-offset-0 text-primary-green-dark dark:border-gray-400 dark:bg-primary-off-black;
  }

  .input {
    @apply block w-full py-1 px-1.5 text-base font-normal placeholder:text-text-disabled disabled:text-text-grey disabled:bg-secondary-grey-light-2 rounded border border-secondary-grey-dark focus:border-secondary-grey-dark focus:ring-0 disabled:cursor-not-allowed dark:bg-primary-off-black dark:text-white dark:border-secondary-grey-light;
  }

  .input-error {
    @apply text-status-red border border-status-red focus:border-status-red;
  }

  .radio {
    @apply w-6 h-6 border border-gray-500 focus:ring-0 focus:ring-offset-0 text-primary-green-dark dark:border-gray-400 dark:bg-primary-off-black;
  }

  /* Typography */

  .typography-display-large {
    @apply font-serif text-5xl font-medium tracking-[0.2px] !leading-normal lg:text-7xl;
  }

  .typography-large {
    @apply font-serif text-[32px] font-medium tracking-normal !leading-tight lg:text-5xl lg:font-normal;
  }

  .typography-medium {
    @apply font-serif text-2xl font-normal tracking-normal !leading-tight lg:text-[32px];
  }

  .typography-small {
    @apply font-serif text-xl font-normal tracking-normal !leading-tight lg:text-2xl;
  }

  .typography-heading-1 {
    @apply font-sans text-[32px] font-semibold tracking-[0.2px] !leading-tight lg:text-[40px];
  }

  .typography-heading-2 {
    @apply font-sans text-[28px] font-semibold tracking-normal !leading-tight lg:text-[32px];
  }

  .typography-heading-3 {
    @apply font-sans text-2xl font-semibold tracking-normal !leading-tight lg:text-[28px];
  }

  .typography-heading-4 {
    @apply font-sans text-xl font-semibold tracking-[0.2px] !leading-tight lg:text-2xl;
  }

  .typography-heading-5 {
    @apply font-sans text-lg font-bold tracking-[0.3px] !leading-tight lg:text-xl;
  }

  .typography-subtitle-1 {
    @apply font-sans text-lg font-semibold tracking-normal !leading-tight;
  }

  .typography-subtitle-2 {
    @apply font-sans text-base font-semibold tracking-normal !leading-tight;
  }

  .typography-body-large {
    @apply font-sans text-lg font-normal tracking-normal !leading-tight;
  }

  .typography-body-regular {
    @apply font-sans text-base font-normal tracking-normal !leading-tight;
  }

  .typography-body-small {
    @apply font-sans text-sm font-normal tracking-normal !leading-tight;
  }

  .typography-paragraph {
    @apply font-sans text-base font-normal tracking-normal !leading-normal;
  }

  .typography-fine-print-bold {
    @apply font-sans text-xs font-semibold tracking-[0.2px] !leading-normal;
  }

  .typography-fine-print {
    @apply font-sans text-xs font-normal tracking-normal !leading-normal;
  }

  .typography-button {
    @apply font-sans text-base font-semibold tracking-[0.2px] !leading-tight;
  }

  .typography-badge {
    @apply font-sans text-sm font-semibold tracking-[0.2px] !leading-tight;
  }
}

html,
body {
  @apply h-full text-text-main bg-secondary-grey-light-2 dark:bg-primary-off-black dark:text-white;
}

.invalid-feedback {
  @apply block mt-1 text-sm text-status-red font-normal tracking-normal !leading-tight;
}

h1 {
  @apply typography-heading-1;
}

h2 {
  @apply typography-heading-2;
}

h3 {
  @apply typography-heading-3;
}

h4 {
  @apply typography-heading-4;
}

h5 {
  @apply typography-heading-5;
}

thead th {
  @apply py-3 px-6 dark:bg-gray-800;
}

tbody td {
  @apply py-4 px-6 border-t-[1px] border-secondary-grey-light-2 dark:border-gray-700;
}

img,
video {
  @apply max-w-none;
}

ul {
  @apply list-disc py-0 px-10;
}

ol {
  @apply list-decimal py-0 px-10;
}

.has-tooltip {
  @apply relative;
}

.tooltip {
  @apply invisible absolute z-50 text-white bg-primary-grey-dark w-64 text-sm font-normal right-0 top-[calc(100%+4px)] shadow-md rounded-md ring-1 ring-black ring-opacity-5 p-4 dark:bg-gray-700 dark:text-white;
}

.has-tooltip:hover .tooltip {
  @apply visible;
}

.fade-in-scale {
  animation: 0.2s ease-in 0s normal forwards 1 fade-in-scale-keys;
}

.fade-out-scale {
  animation: 0.2s ease-out 0s normal forwards 1 fade-out-scale-keys;
}

.fade-in {
  animation: 0.2s ease-out 0s normal forwards 1 fade-in-keys;
}
.fade-out {
  animation: 0.2s ease-out 0s normal forwards 1 fade-out-keys;
}

@keyframes fade-in-scale-keys {
  0% {
    scale: 0.95;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}

@keyframes fade-out-scale-keys {
  0% {
    scale: 1;
    opacity: 1;
  }
  100% {
    scale: 0.95;
    opacity: 0;
  }
}

@keyframes fade-in-keys {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-out-keys {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

[x-cloak] {
  display: none !important;
}

.logo-font {
  font-family: 'Baskervville SC', serif;
  font-weight: 400;
  font-style: normal;
}

/* Not sure why but Petal pagination css not coming through properly */

.hero-chevron-left-solid {
  --hero-chevron-left: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5"/></svg>');
  -webkit-mask: var(--hero-chevron-left);
  mask: var(--hero-chevron-left);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-chevron-right-solid {
  --hero-chevron-right: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5"/></svg>');
  -webkit-mask: var(--hero-chevron-right);
  mask: var(--hero-chevron-right);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.pc-pagination__inner {
  padding-left: 0;
  padding-right: 0;
}

.pc-pagination__inner li {
  list-style-type: none;
}

/* Petal styles getting overridden by hero icons styles */
.pc-button__spinner-icon--xs {
  height: 0.75rem;
  width: 0.75rem;
}

.pc-button__spinner-icon--sm {
  height: 1rem;
  width: 1rem;
}

.pc-button__spinner-icon--md {
  height: 1.25rem;
  width: 1.25rem;
}

.pc-button__spinner-icon--lg {
  height: 1.25rem;
  width: 1.25rem;
}

.pc-button__spinner-icon--xl {
  height: 1.5rem;
  width: 1.5rem;
}

/* Chat Component Streaming Content Isolation */
.streaming-content-container {
  /* Create a new stacking context to isolate content */
  position: relative;
  z-index: 0;
  /* Prevent content from affecting layout outside this container */
  contain: layout style;
  /* Ensure content doesn't overflow and affect other elements */
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.streaming-content-wrapper {
  /* Additional containment for streaming HTML */
  display: block;
  /* Prevent incomplete tables or other block elements from breaking layout */
  overflow: hidden;
  /* Ensure proper text flow */
  white-space: normal;
}

/* Ensure streaming tables don't break layout */
.streaming-content-wrapper table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

.streaming-content-wrapper td,
.streaming-content-wrapper th {
  word-wrap: break-word;
  overflow-wrap: break-word;
}
