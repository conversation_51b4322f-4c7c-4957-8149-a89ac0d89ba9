defmodule HadesWeb.ChatLive.AgentsIndex do
  @moduledoc """
  Landing page showing all available AI agents with cards for each agent
  """
  use HadesWeb, :live_view

  alias Gaia.Admins
  alias HadesWeb.Agents.AgentConfig

  on_mount(HadesWeb.AdminUserLiveAuth)

  @impl true
  def mount(_params, _session, socket) do
    agents = AgentConfig.all_agents()
    changeset = Admins.change_agent_question(%Gaia.Admins.AgentQuestion{})

    {:ok,
     socket
     |> assign(:menu, :chat)
     |> assign(:page_title, "AI Agents")
     |> assign(:agents, agents)
     |> assign(:form, to_form(changeset))
     |> assign(:show_success, false)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          AI Agents
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Choose from our specialized AI agents, each designed to excel at specific tasks.
          Click on any agent to start a conversation and experience their unique capabilities.
        </p>
      </div>
      
    <!-- Agents Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <%= for agent <- @agents do %>
          <div class="group relative">
            <.agent_card agent={agent} />
          </div>
        <% end %>
      </div>
      
    <!-- Feedback Form -->
      <div class="mt-16">
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
          <div class="max-w-2xl mx-auto">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4 text-center">
              Help Us Build Better AI Agents
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-8 text-center">
              What would you like to ask Charlie in the future? Share your ideas to help us prioritize new features and capabilities.
            </p>

            <%= if @show_success do %>
              <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div class="flex items-center">
                  <PC.icon
                    name="hero-check-circle"
                    class="w-5 h-5 text-green-600 dark:text-green-400 mr-2"
                  />
                  <p class="text-green-800 dark:text-green-200 font-medium">
                    Thank you for your feedback! Your suggestion has been submitted.
                  </p>
                </div>
              </div>
            <% end %>

            <.form for={@form} phx-submit="submit_feedback" class="space-y-6">
              <div>
                <PC.field
                  field={@form[:question]}
                  type="textarea"
                  label="What would you like to ask Charlie? *"
                  placeholder="e.g., Can you analyze our email campaign performance and suggest improvements?"
                  rows="4"
                />
              </div>

              <div>
                <PC.field
                  field={@form[:reason]}
                  type="textarea"
                  label="Why would this be helpful? (Optional)"
                  placeholder="e.g., This would save me hours of manual analysis each week..."
                  rows="3"
                />
              </div>

              <div class="text-center">
                <button
                  type="submit"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <PC.icon name="hero-paper-airplane" class="w-5 h-5 mr-2" /> Submit Suggestion
                </button>
              </div>
            </.form>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("submit_feedback", %{"agent_question" => params}, socket) do
    current_admin_user = socket.assigns.current_admin_user

    params_with_user = Map.put(params, "admin_user_id", current_admin_user.id)

    case Admins.create_agent_question(params_with_user) do
      {:ok, _agent_question} ->
        # Reset form and show success message
        changeset = Admins.change_agent_question(%Gaia.Admins.AgentQuestion{})

        {:noreply,
         socket
         |> assign(:form, to_form(changeset))
         |> assign(:show_success, true)}

      {:error, changeset} ->
        {:noreply, assign(socket, :form, to_form(changeset))}
    end
  end

  # Agent card component
  attr :agent, :map, required: true

  defp agent_card(assigns) do
    color_classes = AgentConfig.get_color_classes(assigns.agent.color_scheme)
    is_coming_soon = Map.get(assigns.agent, :coming_soon, false)

    assigns =
      assigns
      |> assign(:color_classes, color_classes)
      |> assign(:is_coming_soon, is_coming_soon)

    ~H"""
    <div class="relative h-full">
      <!-- Coming Soon Badge -->
      <%= if @is_coming_soon do %>
        <div class="absolute -top-3 -right-3 z-10">
          <span class="bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
            Coming Soon
          </span>
        </div>
      <% end %>

      <%= if @is_coming_soon do %>
        <!-- Coming Soon Card (Not Clickable) -->
        <div class={[
          "bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 h-full flex flex-col",
          "opacity-75 cursor-not-allowed"
        ]}>
          <!-- Agent Icon and Name -->
          <div class="flex items-center mb-4">
            <div class={[
              "w-12 h-12 rounded-lg flex items-center justify-center mr-4",
              @color_classes.primary
            ]}>
              <PC.icon name={@agent.icon} class="w-6 h-6" />
            </div>
            <div>
              <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                {@agent.name}
              </h3>
              <div class={[
                "text-sm font-medium",
                @color_classes.accent
              ]}>
                Main AI Assistant
              </div>
            </div>
          </div>
          
    <!-- Description -->
          <p class="text-gray-600 dark:text-gray-400 mb-6 flex-grow">
            {@agent.description}
          </p>
          
    <!-- Capabilities -->
          <div class="mb-6">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Key Capabilities:
            </h4>
            <div class="flex flex-wrap gap-2">
              <%= for capability <- Enum.take(@agent.capabilities, 3) do %>
                <span class={[
                  "px-2 py-1 text-xs rounded-full",
                  @color_classes.secondary
                ]}>
                  {capability}
                </span>
              <% end %>
              <%= if length(@agent.capabilities) > 3 do %>
                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                  +{length(@agent.capabilities) - 3} more
                </span>
              <% end %>
            </div>
          </div>
          
    <!-- Coming Soon Message -->
          <div class="mt-auto">
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 text-center">
              <PC.icon
                name="hero-clock"
                class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mx-auto mb-2"
              />
              <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Currently in development
              </p>
              <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                Available soon
              </p>
            </div>
          </div>
        </div>
      <% else %>
        <!-- Regular Clickable Card -->
        <.link
          navigate={~p"/chat/#{@agent.id}"}
          class="block h-full transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
        >
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 h-full flex flex-col">
            <!-- Agent Icon and Name -->
            <div class="flex items-center mb-4">
              <div class={[
                "w-12 h-12 rounded-lg flex items-center justify-center mr-4",
                @color_classes.primary
              ]}>
                <PC.icon name={@agent.icon} class="w-6 h-6" />
              </div>
              <div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                  {@agent.name}
                </h3>
                <div class={[
                  "text-sm font-medium",
                  @color_classes.accent
                ]}>
                  Specialized Agent
                </div>
              </div>
            </div>
            
    <!-- Description -->
            <p class="text-gray-600 dark:text-gray-400 mb-6 flex-grow">
              {@agent.detailed_description}
            </p>
            
    <!-- Capabilities -->
            <div class="mb-6">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Key Capabilities:
              </h4>
              <div class="flex flex-wrap gap-2">
                <%= for capability <- Enum.take(@agent.capabilities, 3) do %>
                  <span class={[
                    "px-2 py-1 text-xs rounded-full",
                    @color_classes.secondary
                  ]}>
                    {capability}
                  </span>
                <% end %>
                <%= if length(@agent.capabilities) > 3 do %>
                  <span class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                    +{length(@agent.capabilities) - 3} more
                  </span>
                <% end %>
              </div>
            </div>
            
    <!-- Example Queries -->
            <div class="mb-6">
              <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Try asking:
              </h4>
              <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <%= for query <- Enum.take(@agent.example_queries, 2) do %>
                  <li class="flex items-start">
                    <span class="text-gray-400 mr-2">•</span> "{query}"
                  </li>
                <% end %>
              </ul>
            </div>
            
    <!-- CTA Button -->
            <div class="mt-auto">
              <div class={[
                "w-full py-2 px-4 rounded-lg text-center font-medium transition-colors duration-200",
                @color_classes.primary,
                @color_classes.hover
              ]}>
                Start Chatting →
              </div>
            </div>
          </div>
        </.link>
      <% end %>
    </div>
    """
  end
end
