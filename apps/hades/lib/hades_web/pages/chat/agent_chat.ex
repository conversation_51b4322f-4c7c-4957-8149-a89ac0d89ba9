defmodule HadesWeb.ChatLive.AgentChat do
  @moduledoc """
  Individual agent chat page that uses the reusable chat component
  """
  use HadesWeb, :live_view

  alias HadesWeb.Agents.AgentConfig
  alias HadesWeb.Agents.CampaignAnalystAgent
  alias HadesWeb.Agents.GeneralAgent
  alias HadesWeb.Components.ChatComponent

  require Logger

  on_mount(HadesWeb.AdminUserLiveAuth)

  @impl true
  def mount(%{"agent_id" => agent_id}, _session, socket) do
    case AgentConfig.get_agent(agent_id) do
      nil ->
        {:ok,
         socket
         |> put_flash(:error, "Agent not found")
         |> redirect(to: ~p"/chat")}

      agent ->
        # Create a session token for this chat session to maintain conversation context
        session_token = 16 |> :crypto.strong_rand_bytes() |> Base.encode16(case: :lower)

        {:ok,
         socket
         |> assign(:menu, :chat)
         |> assign(:page_title, "Chat with #{agent.name}")
         |> assign(:agent, agent)
         |> assign(:agent_id, agent_id)
         |> assign(:session_token, session_token)
         |> assign(:llm_chain, nil)}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <.live_component module={ChatComponent} id="chat-component" agent={@agent} />
    </div>
    """
  end

  @impl true
  def handle_info({:start_ai_response, message, system_prompt, _component_id, company_profile_id}, socket) do
    # Get or create the LangChain instance to maintain conversation context with company context
    llm_chain =
      get_or_create_llm_chain(
        socket.assigns.llm_chain,
        system_prompt,
        company_profile_id
      )

    # Start the AI response process with streaming enabled and conversation context
    pid = self()

    get_agent_module(socket.assigns.agent_id).get_streaming_chat_response_with_chain(
      message,
      llm_chain,
      pid
    )

    {:noreply, socket}
  end

  @impl true
  def handle_info({:ai_stream_chunk, chunk}, socket) do
    send_update(ChatComponent,
      id: "chat-component",
      action: :ai_stream_chunk,
      chunk: chunk
    )

    {:noreply, socket}
  end

  @impl true
  def handle_info({:ai_stream_complete, response, updated_chain}, socket) do
    # Add AI response to chat
    ai_message = %{
      role: :assistant,
      content: response,
      timestamp: DateTime.utc_now()
    }

    send_update(ChatComponent,
      id: "chat-component",
      action: :ai_response_complete,
      message: ai_message
    )

    # Store the updated chain to maintain conversation context
    {:noreply, assign(socket, :llm_chain, updated_chain)}
  end

  @impl true
  def handle_info({:ai_stream_error, error}, socket) do
    Logger.error("AI stream error: #{inspect(error)}")

    error_message = %{
      role: :assistant,
      content: "I apologize, but I encountered an error while processing your request. Please try again.",
      timestamp: DateTime.utc_now()
    }

    send_update(ChatComponent,
      id: "chat-component",
      action: :ai_response_complete,
      message: error_message
    )

    {:noreply, socket}
  end

  # Private helper functions

  defp get_agent_module("campaign_analyst"), do: CampaignAnalystAgent
  defp get_agent_module(_), do: GeneralAgent

  defp get_or_create_llm_chain(nil, system_prompt, company_profile_id) do
    # Create a new LangChain instance with the system prompt and company context
    alias LangChain.Chains.LLMChain
    alias LangChain.ChatModels.ChatOpenAI
    alias LangChain.Message

    chat_model = ChatOpenAI.new!(%{model: "gpt-4o", stream: true, temperature: 0.0})
    context = %{company_profile_id: company_profile_id}

    %{llm: chat_model, custom_context: context, verbose: true}
    |> LLMChain.new!()
    |> LLMChain.add_message(Message.new_system!(system_prompt))
  end

  defp get_or_create_llm_chain(existing_chain, _system_prompt, _company_profile_id) when not is_nil(existing_chain) do
    # Return the existing chain to maintain conversation context
    existing_chain
  end
end
