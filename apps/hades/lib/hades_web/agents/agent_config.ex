defmodule HadesWeb.Agents.AgentConfig do
  @moduledoc """
  Configuration module for all available AI agents.
  Defines agent metadata, capabilities, and system prompts.
  """

  @type agent :: %{
          id: String.t(),
          name: String.t(),
          detailed_description: String.t(),
          icon: String.t(),
          color_scheme: String.t(),
          capabilities: [String.t()],
          system_prompt: String.t(),
          example_queries: [String.t()]
        }

  @doc """
  Returns all available agents
  """
  @spec all_agents() :: [agent()]
  def all_agents do
    [
      %{
        id: "campaign_analyst",
        name: "Campaign Analyst",
        detailed_description:
          "A specialized AI agent focused on marketing campaign analysis and optimization. It helps you understand campaign performance.",
        icon: "hero-chart-pie",
        color_scheme: "blue",
        capabilities: [
          "Campaign performance analysis",
          "Campaign Information fetching"
        ],
        system_prompt: HadesWeb.Agents.CampaignAnalystAgent.get_system_prompt(),
        example_queries: [
          "How did my last email do?"
        ]
      },
      %{
        id: "charlie",
        name: "<PERSON>",
        description: "Your main AI assistant - a powerful, multi-functional agent for general tasks.",
        detailed_description:
          "<PERSON> is your primary AI assistant, designed to handle a wide variety of tasks and provide comprehensive support across different domains. As our most advanced agent, <PERSON> can adapt to various contexts and provide intelligent assistance for complex, multi-faceted problems.",
        icon: "hero-cpu-chip",
        color_scheme: "indigo",
        capabilities: [
          "General problem solving",
          "Multi-domain expertise",
          "Complex task coordination",
          "Strategic planning",
          "Research and analysis",
          "Creative solutions"
        ],
        system_prompt:
          "You are Charlie, the main AI assistant. You are intelligent, helpful, and capable of handling a wide variety of tasks. Adapt your communication style to the user's needs and provide comprehensive, thoughtful responses. You can work across multiple domains and help coordinate complex tasks.",
        example_queries: [
          "Help me plan a product launch strategy",
          "What's the best approach for this business challenge?",
          "Analyze this situation and provide recommendations",
          "Help me brainstorm solutions for this problem"
        ],
        coming_soon: true
      }
    ]
  end

  @doc """
  Get a specific agent by ID
  """
  @spec get_agent(String.t()) :: agent() | nil
  def get_agent(agent_id) do
    Enum.find(all_agents(), &(&1.id == agent_id))
  end

  @doc """
  Get agent color classes for UI styling
  """
  @spec get_color_classes(String.t()) :: %{
          primary: String.t(),
          secondary: String.t(),
          accent: String.t(),
          hover: String.t()
        }
  def get_color_classes(color_scheme) do
    case color_scheme do
      "blue" ->
        %{
          primary: "bg-blue-500 text-white",
          secondary: "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300",
          accent: "text-blue-600 dark:text-blue-400",
          hover: "hover:bg-blue-600"
        }

      "green" ->
        %{
          primary: "bg-green-500 text-white",
          secondary: "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300",
          accent: "text-green-600 dark:text-green-400",
          hover: "hover:bg-green-600"
        }

      "purple" ->
        %{
          primary: "bg-purple-500 text-white",
          secondary: "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300",
          accent: "text-purple-600 dark:text-purple-400",
          hover: "hover:bg-purple-600"
        }

      "orange" ->
        %{
          primary: "bg-orange-500 text-white",
          secondary: "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300",
          accent: "text-orange-600 dark:text-orange-400",
          hover: "hover:bg-orange-600"
        }

      "indigo" ->
        %{
          primary: "bg-indigo-500 text-white",
          secondary: "bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300",
          accent: "text-indigo-600 dark:text-indigo-400",
          hover: "hover:bg-indigo-600"
        }

      _ ->
        %{
          primary: "bg-gray-500 text-white",
          secondary: "bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300",
          accent: "text-gray-600 dark:text-gray-400",
          hover: "hover:bg-gray-600"
        }
    end
  end
end
