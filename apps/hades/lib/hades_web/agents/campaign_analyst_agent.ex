defmodule HadesWeb.Agents.CampaignAnalystAgent do
  @moduledoc """
  Campaign Analyst Agent - Specialized AI agent for marketing campaign analysis
  """

  alias Gaia.Comms
  alias HadesWeb.Agents.HtmlSanitizer
  alias LangChain.Chains.LLMChain
  alias LangChain.Function
  alias LangChain.FunctionParam
  alias LangChain.Message
  alias LangChain.MessageDelta

  @doc """
  Get a streaming chat response with function calling capabilities
  """
  def get_streaming_chat_response_with_chain(user_message, llm_chain, recipient_pid) do
    Task.start(fn ->
      try do
        user_message
        |> prepare_chain(llm_chain, recipient_pid)
        |> add_tools()
        |> execute_chain_with_tools(recipient_pid)
      rescue
        error ->
          send_error_message(recipient_pid, "Unexpected error: #{inspect(error)}")
      end
    end)
  end

  defp add_tools(chain) do
    LLMChain.add_tools(chain, [
      Function.new!(%{
        name: "search_campaigns",
        description: """
         Function to search for campaigns
        """,
        parameters: [
          FunctionParam.new!(%{
            name: "order_key",
            type: :string,
            description: """
            If it is time related, default to use sent_at
            Other potential values are:
            inserted_at,
            scheduled_at
            """,
            required: true
          }),
          FunctionParam.new!(%{
            name: "order_type",
            type: :string,
            description: "The type of the order, should only be desc or asc",
            required: true
          }),
          FunctionParam.new!(%{
            name: "is_draft",
            type: :string,
            description: """
            To determine if the campaign still in draft or not.
            If user do not specify, the value will be false
            """
          }),
          FunctionParam.new!(%{
            name: "type",
            type: :string,
            description: """
            The type of the campaign
            If user do not specify, the value will be all_except_welcome
            Otherwise potential values are:
            Announcement
            Update: which could also be called News
            """
          })
        ],
        function: &search_campaigns/2
      }),
      Function.new!(%{
        name: "get_campaign_details",
        description:
          "Retrieve detailed information about a specific email campaign by ID. Use this to analyze campaign performance, content, and metrics.",
        parameters: [
          FunctionParam.new!(%{
            name: "campaign_id",
            type: :integer,
            description: "The unique ID of the email campaign",
            required: true
          })
        ],
        function: &get_campaign_details/2
      })
    ])
  end

  defp prepare_chain(user_message, llm_chain, recipient_pid) do
    callback_handler = create_streaming_callback_handler(recipient_pid)

    llm_chain
    |> LLMChain.add_message(Message.new_user!(user_message))
    |> LLMChain.add_callback(callback_handler)
  end

  defp execute_chain_with_tools(updated_chain, recipient_pid) do
    case LLMChain.run(updated_chain, mode: :while_needs_response) do
      {:ok, chain_with_response} ->
        chain_with_response
        |> LLMChain.execute_tool_calls()
        |> handle_tool_execution_result(recipient_pid)

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        send_error_message(recipient_pid, "Chat failed: #{inspect(error)}")
    end
  end

  defp handle_tool_execution_result(chain_after_tools, recipient_pid) do
    if chain_after_tools.needs_response do
      continue_conversation_after_tools(chain_after_tools, recipient_pid)
    else
      send_final_response(chain_after_tools, recipient_pid)
    end
  end

  defp continue_conversation_after_tools(chain_after_tools, recipient_pid) do
    case LLMChain.run(chain_after_tools, mode: :while_needs_response) do
      {:ok, final_chain} ->
        send_final_response(final_chain, recipient_pid)

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        send_error_message(recipient_pid, "Chat failed: #{inspect(error)}")
    end
  end

  defp send_final_response(chain, recipient_pid) do
    case extract_message_content(chain) do
      {:ok, content} ->
        sanitized_content = HtmlSanitizer.sanitize(content)
        send(recipient_pid, {:ai_stream_complete, sanitized_content, chain})

      :error ->
        send_error_message(recipient_pid, "No response received from AI model")
    end
  end

  defp extract_message_content(chain) do
    case List.last(chain.messages) do
      %Message{content: content} when not is_nil(content) ->
        {:ok, content}

      _ ->
        :error
    end
  end

  defp create_streaming_callback_handler(recipient_pid) do
    %{
      on_llm_new_delta: fn _chain, delta ->
        handle_stream_delta(delta, recipient_pid)
      end
    }
  end

  defp handle_stream_delta(%MessageDelta{content: content}, recipient_pid) when not is_nil(content) do
    # Note: We don't sanitize streaming content as it's partial and may break HTML tags
    # Final sanitization happens in send_final_response
    send(recipient_pid, {:ai_stream_chunk, content})
  end

  defp handle_stream_delta(_, _recipient_pid), do: :ok

  defp send_error_message(recipient_pid, error_message) do
    send(recipient_pid, {:ai_stream_error, error_message})
  end

  def get_system_prompt do
    """
    You are a Campaign Analyst AI assistant. You specialize in marketing campaign analysis.

    IMPORTANT FORMATTING REQUIREMENTS:
    - Always format your responses using clean, semantic HTML
    - Use smaller headings: start with <h3> for main title, <h4> for sections, <h5> for subsections
    - Use <p> tags for paragraphs and proper line breaks
    - Use <ul> and <li> for lists and bullet points
    - Use <strong> for emphasis and <em> for italics
    - For data tables, use proper <table>, <thead>, <tbody>, <tr>, <th>, <td> structure
    - Use simple <div> containers for grouping content without background colors
    - Focus on typography and structure rather than colors or visual styling

    SECURITY REQUIREMENTS:
    - Only use safe HTML tags: h3, h4, h5, h6, p, div, span, strong, em, ul, ol, li, table, thead, tbody, tr, th, td, br
    - Never include script tags, iframe, object, embed, or any executable content
    - Do not use class attributes or styling
    - Do not include any external links or resources

    CAMPAIGN DATA FORMATTING GUIDELINES:
    When displaying campaign details, always format the data consistently:

    CAMPAIGN OVERVIEW TABLE:
    - Create a clean table with campaign basic info (ID, Campaign Name, Subject, Sent Date)
    - Use proper table headers and ensure data is well-aligned
    - Format dates in a readable format (e.g., "Dec 15, 2024 at 2:30 PM")

    PERFORMANCE METRICS TABLE:
    - Display metrics in a separate table with clear labels
    - Format rates as percentages (e.g., "25.4%" not "0.254")
    - Show counts as whole numbers with proper labels
    - Group related metrics logically (engagement vs. negative metrics)

    KEY INSIGHTS SECTION:
    - Use bullet points to highlight important findings
    - Compare rates to industry benchmarks when relevant
    - Identify standout performance (high/low rates)
    - Suggest actionable improvements based on the data

    CONTENT STRUCTURE:
    - Start with a compact <h3> heading that summarizes the campaign
    - Use <h4> for "Campaign Overview", "Performance Metrics", "Key Insights"
    - Present campaign data in clean, organized tables
    - Use concise bullet points for analysis and recommendations
    - Keep the response focused and actionable
    - Maintain a professional but accessible tone
    """
  end

  ###### TOOLS ######
  def get_campaign_details(%{"campaign_id" => id}, _context) do
    case Comms.get_email(id) do
      %Gaia.Comms.Email{} = email ->
        total_emails = Comms.count_total_recipient_events_by_email(id, :Total)
        clicked_emails = Comms.count_total_recipient_events_by_email(id, :Click)
        open_emails = Comms.count_total_recipient_events_by_email(id, :Open)
        unsubscribed_emails = Comms.count_total_recipient_events_by_email(id, :Unsubscribed)
        bounce_emails = Comms.count_total_recipient_events_by_email(id, :Bounce)
        complaint_emails = Comms.count_total_recipient_events_by_email(id, :Complaint)

        {:ok,
         inspect(%{
           id: email.id,
           campaign: email.campaign_name,
           subject: email.subject,
           sent_at: email.sent_at,
           total_emails_count: total_emails,
           clicked_rate: clicked_emails / total_emails,
           open_rate: open_emails / total_emails,
           bounce_emails_count: bounce_emails,
           complaint_emails_count: complaint_emails,
           unsubscribed_emails_count: unsubscribed_emails
         })}

      nil ->
        {:error, "Email with ID #{id} not found"}
    end
  end

  def search_campaigns(args, %{company_profile_id: company_profile_id}) when not is_nil(company_profile_id) do
    orders = [
      %{key: Map.get(args, "order_key", "sent_at"), value: Map.get(args, "order_type", "desc")},
      # Limit to 3 results
      %{key: "limit", value: 3}
    ]

    filters = [
      %{key: "type", value: Map.get(args, "type", "all_except_welcome")},
      %{key: "is_draft", value: Map.get(args, "is_draft", "false")}
    ]

    # Query emails using the existing function
    emails =
      %{filters: filters, orders: orders}
      |> Comms.emails_query_by_company_profile_id(company_profile_id)
      |> Gaia.Repo.all()

    # Return only IDs as requested
    campaign_ids = Enum.map(emails, & &1.id)

    {:ok, inspect(campaign_ids)}
  end

  def search_campaigns(_args, _context) do
    {:ok, "Terminate! Company profile id is missing"}
  end
end
