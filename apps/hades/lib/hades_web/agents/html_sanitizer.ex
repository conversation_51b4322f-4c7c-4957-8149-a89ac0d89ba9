defmodule HadesWeb.Agents.HtmlSanitizer do
  @moduledoc """
  HTML sanitization utilities for AI agent responses.

  This module provides secure HTML sanitization to ensure that AI-generated
  HTML content is safe to display in the web interface while preserving
  semantic structure and formatting.
  """

  @doc """
  Sanitizes HTML content by removing dangerous tags and attributes.

  Allowed tags: h3-h6, p, div, span, strong, em, ul, ol, li, table, thead, tbody, tr, th, td, br
  All attributes are removed to ensure clean semantic HTML without styling.

  ## Examples

      iex> HadesWeb.Agents.HtmlSanitizer.sanitize("<h1>Title</h1><script>alert('xss')</script>")
      "<h1>Title</h1>"

      iex> HadesWeb.Agents.HtmlSanitizer.sanitize("<div class='danger'>Content</div>")
      "<div>Content</div>"
  """
  def sanitize(content) when is_binary(content) do
    content
    |> remove_dangerous_tags()
    |> convert_large_headings()
    |> remove_all_attributes()
  end

  def sanitize(content), do: content

  @doc """
  Sanitizes streaming HTML content with additional safety measures for incomplete tags.

  This function is specifically designed for streaming content where HTML tags
  might be incomplete and could potentially break page layout.
  """
  def sanitize_streaming(content) when is_binary(content) do
    content
    |> remove_dangerous_tags()
    |> convert_large_headings()
    |> remove_all_attributes()
    |> ensure_safe_streaming_structure()
  end

  def sanitize_streaming(content), do: content

  # Converts large headings (h1, h2) to smaller ones (h3, h4) for better visual hierarchy.
  defp convert_large_headings(content) do
    content
    |> String.replace(~r/<h1([^>]*)>/i, "<h3\\1>")
    |> String.replace(~r/<\/h1>/i, "</h3>")
    |> String.replace(~r/<h2([^>]*)>/i, "<h4\\1>")
    |> String.replace(~r/<\/h2>/i, "</h4>")
  end

  # Removes dangerous HTML tags that could be used for XSS attacks.
  defp remove_dangerous_tags(content) do
    dangerous_patterns = [
      ~r/<script[^>]*>.*?<\/script>/is,
      ~r/<iframe[^>]*>.*?<\/iframe>/is,
      ~r/<object[^>]*>.*?<\/object>/is,
      ~r/<embed[^>]*>.*?<\/embed>/is,
      ~r/<link[^>]*>/is,
      ~r/<meta[^>]*>/is,
      ~r/<style[^>]*>.*?<\/style>/is,
      ~r/<form[^>]*>.*?<\/form>/is,
      ~r/<input[^>]*>/is,
      ~r/<button[^>]*>.*?<\/button>/is,
      ~r/javascript:/i,
      ~r/on\w+\s*=/i
    ]

    Enum.reduce(dangerous_patterns, content, fn pattern, acc ->
      String.replace(acc, pattern, "")
    end)
  end

  # Removes all HTML attributes to keep only clean semantic markup.
  defp remove_all_attributes(content) do
    # Remove attributes in quoted form: attribute="value" or attribute='value'
    content = String.replace(content, ~r/\s+\w+\s*=\s*["'][^"']*["']/i, "")

    # Remove attributes in unquoted form: attribute=value
    content = String.replace(content, ~r/\s+\w+\s*=\s*[^\s>]+/i, "")

    content
  end

  # Ensures streaming content has safe structure that won't break page layout
  defp ensure_safe_streaming_structure(content) do
    content
    |> close_incomplete_tags()
    |> wrap_orphaned_text()
  end

  # Closes incomplete HTML tags that could break layout
  defp close_incomplete_tags(content) do
    # List of tags that need to be closed if left open
    self_closing_tags = ["br", "hr", "img", "input", "meta", "link"]

    # Find all opening tags
    opening_tags =
      ~r/<(\w+)[^>]*>/i
      |> Regex.scan(content, capture: :all_but_first)
      |> List.flatten()
      |> Enum.map(&String.downcase/1)
      |> Enum.reject(&(&1 in self_closing_tags))

    # Find all closing tags
    closing_tags =
      ~r/<\/(\w+)>/i
      |> Regex.scan(content, capture: :all_but_first)
      |> List.flatten()
      |> Enum.map(&String.downcase/1)

    # Find unclosed tags
    unclosed_tags = opening_tags -- closing_tags

    # Close unclosed tags in reverse order (LIFO)
    unclosed_tags
    |> Enum.reverse()
    |> Enum.reduce(content, fn tag, acc ->
      acc <> "</#{tag}>"
    end)
  end

  # Wraps orphaned text in paragraph tags for better structure
  defp wrap_orphaned_text(content) do
    # This is a simple implementation - could be enhanced further
    content
  end

  @doc """
  Returns a list of allowed HTML tags for documentation purposes.
  """
  def allowed_tags do
    [
      "h3",
      "h4",
      "h5",
      "h6",
      "p",
      "div",
      "span",
      "strong",
      "em",
      "ul",
      "ol",
      "li",
      "table",
      "thead",
      "tbody",
      "tr",
      "th",
      "td",
      "br"
    ]
  end
end
