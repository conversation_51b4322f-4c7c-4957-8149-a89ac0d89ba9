defmodule HadesWeb.Agents.GeneralAgent do
  @moduledoc """
  General Agent
  """

  alias Lang<PERSON>hain.Chains.LLMChain
  alias LangChain.ChatModels.ChatOpenAI
  alias LangChain.Message
  alias LangChain.MessageDelta

  @doc """
  Start a streaming chat response and send updates to the given process
  """
  def start_streaming_chat(user_message, recipient_pid) do
    Task.start(fn ->
      case get_chat_response(user_message) do
        {:ok, response} ->
          simulate_streaming(response, recipient_pid)
          send(recipient_pid, {:ai_stream_complete, response})

        {:error, error} ->
          send(recipient_pid, {:ai_stream_error, error})
      end
    end)
  end

  defp simulate_streaming(response, recipient_pid) do
    words = String.split(response, " ")

    Enum.reduce(words, "", fn word, acc ->
      chunk = if acc == "", do: word, else: " #{word}"
      send(recipient_pid, {:ai_stream_chunk, chunk})
      Process.sleep(50)
      acc <> chunk
    end)
  end

  @doc """
  Get a simple non-streaming chat response
  """
  def get_chat_response(user_message) do
    case %{llm: ChatOpenAI.new!(%{model: "gpt-4"})}
         |> LLMChain.new!()
         |> LLMChain.add_message(Message.new_user!(user_message))
         |> LLMChain.run() do
      {:ok, %LangChain.Chains.LLMChain{} = chain} ->
        case List.last(chain.messages) do
          %Message{content: content} when not is_nil(content) ->
            {:ok, content}

          _ ->
            {:error, "No response received from AI model"}
        end

      {:error, _chain, %LangChain.LangChainError{} = error} ->
        {:error, "Chat failed: #{inspect(error)}"}
    end
  end

  @doc """
  Get a streaming chat response with a custom system prompt
  """
  def get_streaming_chat_response_with_system_prompt(user_message, system_prompt, recipient_pid) do
    get_streaming_chat_response_with_system_prompt_and_model(
      user_message,
      system_prompt,
      "gpt-4o",
      recipient_pid
    )
  end

  @doc """
  Get a streaming chat response with a custom system prompt and model selection
  """
  def get_streaming_chat_response_with_system_prompt_and_model(user_message, system_prompt, model, recipient_pid) do
    Task.start(fn ->
      try do
        callback_handler = create_streaming_callback_handler(recipient_pid)

        %{llm: ChatOpenAI.new!(%{model: model, stream: true})}
        |> LLMChain.new!()
        |> LLMChain.add_message(Message.new_system!(system_prompt))
        |> LLMChain.add_message(Message.new_user!(user_message))
        |> LLMChain.add_callback(callback_handler)
        |> LLMChain.run()
        |> handle_llm_chain_result(recipient_pid)
      rescue
        error ->
          send_error_message(recipient_pid, "Unexpected error: #{inspect(error)}")
      end
    end)
  end

  defp create_streaming_callback_handler(recipient_pid) do
    %{
      on_llm_new_delta: fn _chain, delta ->
        handle_stream_delta(delta, recipient_pid)
      end
    }
  end

  defp handle_stream_delta(%MessageDelta{content: content}, recipient_pid) when not is_nil(content) do
    send(recipient_pid, {:ai_stream_chunk, content})
  end

  defp handle_stream_delta(_, _recipient_pid), do: :ok

  defp handle_llm_chain_result({:ok, %LangChain.Chains.LLMChain{}}, _recipient_pid) do
    :ok
  end

  defp handle_llm_chain_result({:error, _chain, %LangChain.LangChainError{} = error}, recipient_pid) do
    send_error_message(recipient_pid, "Chat failed: #{inspect(error)}")
  end

  @doc """
  Get a streaming chat response using an existing LangChain to maintain conversation context
  """
  def get_streaming_chat_response_with_chain(user_message, llm_chain, recipient_pid) do
    Task.start(fn ->
      try do
        callback_handler = create_streaming_callback_handler(recipient_pid)

        updated_chain =
          llm_chain
          |> LLMChain.add_message(Message.new_user!(user_message))
          |> LLMChain.add_callback(callback_handler)

        case LLMChain.run(updated_chain) do
          {:ok, final_chain} ->
            case List.last(final_chain.messages) do
              %Message{content: content} when not is_nil(content) ->
                send(recipient_pid, {:ai_stream_complete, content, final_chain})

              _ ->
                send_error_message(recipient_pid, "No response received from AI model")
            end

          {:error, _chain, %LangChain.LangChainError{} = error} ->
            send_error_message(recipient_pid, "Chat failed: #{inspect(error)}")
        end
      rescue
        error ->
          send_error_message(recipient_pid, "Unexpected error: #{inspect(error)}")
      end
    end)
  end

  defp send_error_message(recipient_pid, error_message) do
    send(recipient_pid, {:ai_stream_error, error_message})
  end
end
