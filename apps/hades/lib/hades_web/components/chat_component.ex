defmodule HadesWeb.Components.ChatComponent do
  @moduledoc """
  Reusable chat component that can be used by different agents
  """
  use HadesWeb, :live_component

  alias Gaia.Companies
  alias Gaia.Repo
  alias HadesWeb.Agents.AgentConfig

  @impl true
  def mount(socket) do
    # Get available companies based on environment
    available_companies = get_available_companies()
    default_company = List.first(available_companies)

    {:ok,
     socket
     |> assign(:messages, [])
     |> assign(:current_message, "")
     |> assign(:streaming_response, "")
     |> assign(:is_streaming, false)
     |> assign(:available_companies, available_companies)
     |> assign(:selected_company, default_company)
     |> assign(:company_profile_id, default_company && default_company.id)}
  end

  @impl true
  def update(%{agent: agent} = assigns, socket) do
    color_classes = AgentConfig.get_color_classes(agent.color_scheme)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:color_classes, color_classes)}
  end

  @impl true
  def update(%{action: :ai_stream_chunk, chunk: chunk}, socket) do
    current_response = socket.assigns.streaming_response
    updated_response = current_response <> chunk

    {:ok, assign(socket, :streaming_response, updated_response)}
  end

  @impl true
  def update(%{action: :ai_response_complete, message: ai_message}, socket) do
    updated_messages = socket.assigns.messages ++ [ai_message]

    {:ok,
     socket
     |> assign(:messages, updated_messages)
     |> assign(:streaming_response, "")
     |> assign(:is_streaming, false)}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col h-[calc(100vh-4rem)] max-w-5xl mx-auto bg-white dark:bg-gray-900 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
      <!-- Chat Header -->
      <div class="flex items-center justify-between px-6 py-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center">
          <.link
            navigate={~p"/chat"}
            class="mr-4 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-white dark:hover:bg-gray-600 rounded-lg transition-all duration-200"
          >
            <PC.icon name="hero-arrow-left" class="w-5 h-5" />
          </.link>
          <div class={[
            "w-10 h-10 rounded-xl flex items-center justify-center mr-3 shadow-sm",
            @color_classes.primary
          ]}>
            <PC.icon name={@agent.icon} class="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">{@agent.name}</h1>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <!-- Company Selection Dropdown -->
          <div class="relative" x-data="{ open: false }">
            <button
              @click="open = !open"
              class="px-3 py-2 text-sm border border-gray-300 dark:border-gray-500 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-sm"
            >
              <PC.icon name="hero-building-office" class="w-4 h-4" />
              <span>{@selected_company.name}</span>
              <PC.icon name="hero-chevron-down" class="w-4 h-4" />
            </button>
            <div
              x-show="open"
              @click.outside="open = false"
              x-transition:enter="transition ease-out duration-100"
              x-transition:enter-start="transform opacity-0 scale-95"
              x-transition:enter-end="transform opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-75"
              x-transition:leave-start="transform opacity-100 scale-100"
              x-transition:leave-end="transform opacity-0 scale-95"
              class="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-700 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
            >
              <div class="py-1 max-h-60 overflow-y-auto">
                <%= for company <- @available_companies do %>
                  <button
                    phx-click="select_company"
                    phx-target={@myself}
                    phx-value-company_id={company.id}
                    @click="open = false"
                    class={[
                      "w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200",
                      if(@selected_company && @selected_company.id == company.id,
                        do: "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300",
                        else: "text-gray-700 dark:text-gray-300"
                      )
                    ]}
                  >
                    <div class="font-medium">
                      {company.name}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {company.name}
                    </div>
                  </button>
                <% end %>
              </div>
            </div>
          </div>

          <button
            phx-click="clear_chat"
            phx-target={@myself}
            disabled={Enum.empty?(@messages) and not @is_streaming}
            class="px-4 py-2 text-sm border border-gray-300 dark:border-gray-500 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-sm"
          >
            <PC.icon name="hero-trash" class="w-4 h-4" />
            <span>Clear</span>
          </button>
        </div>
      </div>
      
    <!-- Messages Container -->
      <div
        id={"messages-container-#{@id}"}
        class="flex-1 overflow-y-auto px-6 py-4 space-y-4"
        phx-hook="ScrollToBottom"
      >
        <%= if Enum.empty?(@messages) and not @is_streaming do %>
          <div class="flex items-center justify-center h-full">
            <div class="text-center max-w-2xl mx-auto">
              <div class={[
                "w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",
                @color_classes.primary
              ]}>
                <PC.icon name={@agent.icon} class="w-10 h-10 text-white" />
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                Chat with {@agent.name}
              </h3>
              <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                {@agent.detailed_description}
              </p>
              <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <PC.icon name="hero-light-bulb" class="w-5 h-5 mr-2 text-yellow-500" /> Try asking:
                </h4>
                <ul class="text-left space-y-3">
                  <%= for query <- Enum.take(@agent.example_queries, 3) do %>
                    <li
                      class="flex items-start group cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg p-3 transition-colors duration-200"
                      phx-click="send_example_query"
                      phx-target={@myself}
                      phx-value-query={query}
                    >
                      <div class={[
                        "w-2 h-2 rounded-full mr-3 mt-2 flex-shrink-0",
                        @color_classes.primary
                      ]}>
                      </div>
                      <span class="text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-200">
                        "{query}"
                      </span>
                    </li>
                  <% end %>
                </ul>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-4 text-center">
                  Click on any example above or type your own question below
                </p>
              </div>
            </div>
          </div>
        <% else %>
          <%= for {message, _index} <- Enum.with_index(@messages) do %>
            <div class={[
              "flex chat-message mb-4",
              if(message.role == :user, do: "justify-end", else: "justify-start")
            ]}>
              <div class={[
                "max-w-[75%] rounded-2xl px-4 py-3 message-bubble shadow-md",
                if(message.role == :user,
                  do: "bg-gradient-to-r from-blue-500 to-blue-600 text-white",
                  else:
                    "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"
                )
              ]}>
                <div class="flex items-start space-x-3">
                  <%= if message.role == :assistant do %>
                    <div class={[
                      "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",
                      @color_classes.primary
                    ]}>
                      <PC.icon name={@agent.icon} class="w-4 h-4 text-white" />
                    </div>
                  <% end %>
                  <div class="flex-1 min-w-0">
                    <div class={[
                      "text-xs font-semibold mb-2",
                      if(message.role == :user, do: "text-blue-100", else: @color_classes.accent)
                    ]}>
                      {if message.role == :user, do: "You", else: @agent.name}
                    </div>
                    <div class="text-base leading-relaxed">
                      <%= if message.role == :assistant do %>
                        {Phoenix.HTML.raw(String.trim(message.content))}
                      <% else %>
                        {String.trim(message.content)}
                      <% end %>
                    </div>
                    <div class={[
                      "text-xs mt-2",
                      if(message.role == :user,
                        do: "text-blue-200",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      {Calendar.strftime(message.timestamp, "%I:%M %p")}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
          
    <!-- Streaming Response -->
          <%= if @is_streaming do %>
            <div class="flex justify-start chat-message mb-4">
              <div class="max-w-[75%] rounded-2xl px-4 py-3 message-bubble shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700">
                <div class="flex items-start space-x-3">
                  <div class={[
                    "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 mt-1",
                    @color_classes.primary
                  ]}>
                    <PC.icon name={@agent.icon} class="w-4 h-4 text-white animate-pulse" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class={["text-xs font-semibold mb-2", @color_classes.accent]}>
                      {@agent.name}
                    </div>
                    <!-- Isolated container for streaming content to prevent layout shifts -->
                    <div class="text-base leading-relaxed streaming-content-container">
                      <div class="streaming-content-wrapper">
                        {Phoenix.HTML.raw(render_safe_streaming_content(@streaming_response))}
                        <span class="inline-block w-0.5 h-5 bg-blue-500 animate-pulse ml-1 rounded">
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
      
    <!-- Message Input -->
      <div class="border-t border-gray-200 dark:border-gray-600 px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
        <form phx-submit="send_message" phx-target={@myself} class="flex space-x-3">
          <div class="flex-1">
            <input
              type="text"
              name="message"
              value={@current_message}
              placeholder={"Ask #{@agent.name} anything..."}
              disabled={@is_streaming}
              phx-change="update_message"
              phx-target={@myself}
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-xl shadow-sm transition-all duration-200 text-base"
              autocomplete="off"
            />
          </div>
          <button
            type="submit"
            disabled={String.trim(@current_message) == "" or @is_streaming}
            class={[
              "px-4 py-3 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 shadow-md flex items-center justify-center hover:shadow-lg transform hover:scale-105",
              @color_classes.primary,
              @color_classes.hover
            ]}
          >
            <%= if @is_streaming do %>
              <PC.icon name="hero-stop" class="w-5 h-5" />
            <% else %>
              <PC.icon name="hero-paper-airplane" class="w-5 h-5" />
            <% end %>
          </button>
        </form>
        <div class="mt-3 text-sm text-gray-500 dark:text-gray-400 text-center">
          Press Enter to send • Powered by GPT-4O
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("update_message", %{"message" => message}, socket) do
    {:noreply, assign(socket, :current_message, message)}
  end

  @impl true
  def handle_event("send_example_query", %{"query" => query}, socket) do
    if socket.assigns.is_streaming do
      {:noreply, socket}
      # Add user message to chat

      # Start AI response with agent-specific system prompt

      # Start async AI response
    else
      user_message = %{
        role: :user,
        content: query,
        timestamp: DateTime.utc_now()
      }

      updated_messages = socket.assigns.messages ++ [user_message]

      socket =
        socket
        |> assign(:messages, updated_messages)
        |> assign(:current_message, "")
        |> assign(:is_streaming, true)
        |> assign(:streaming_response, "")

      send(
        self(),
        {:start_ai_response, query, socket.assigns.agent.system_prompt, socket.assigns.id,
         socket.assigns.company_profile_id}
      )

      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("send_message", %{"message" => message}, socket) do
    message = String.trim(message)

    if message != "" and not socket.assigns.is_streaming do
      # Add user message to chat
      user_message = %{
        role: :user,
        content: message,
        timestamp: DateTime.utc_now()
      }

      updated_messages = socket.assigns.messages ++ [user_message]

      # Start AI response with agent-specific system prompt
      socket =
        socket
        |> assign(:messages, updated_messages)
        |> assign(:current_message, "")
        |> assign(:is_streaming, true)
        |> assign(:streaming_response, "")

      # Start async AI response
      send(
        self(),
        {:start_ai_response, message, socket.assigns.agent.system_prompt, socket.assigns.id,
         socket.assigns.company_profile_id}
      )

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("clear_chat", _params, socket) do
    {:noreply,
     socket
     |> assign(:messages, [])
     |> assign(:current_message, "")
     |> assign(:streaming_response, "")
     |> assign(:is_streaming, false)}
  end

  @impl true
  def handle_event("select_company", %{"company_id" => company_id}, socket) do
    company_id = String.to_integer(company_id)
    selected_company = Enum.find(socket.assigns.available_companies, &(&1.id == company_id))

    {:noreply,
     socket
     |> assign(:selected_company, selected_company)
     |> assign(:company_profile_id, selected_company && selected_company.id)}
  end

  # Private helper functions

  defp render_safe_streaming_content(content) when is_binary(content) do
    content
    |> String.trim()
    |> buffer_incomplete_html_tags()
    |> HadesWeb.Agents.HtmlSanitizer.sanitize_streaming()
  end

  defp render_safe_streaming_content(_), do: ""

  # Buffer incomplete HTML tags to prevent layout issues during streaming
  defp buffer_incomplete_html_tags(content) do
    # Check if content ends with an incomplete tag (e.g., "<table" or "<tr>")
    # If so, buffer it until the tag is complete
    case Regex.run(~r/<[^>]*$/, content) do
      nil ->
        # No incomplete tag at the end, safe to render
        content

      [_incomplete_tag] ->
        # Remove the incomplete tag from the end
        content
        |> String.replace(~r/<[^>]*$/, "")
        |> String.trim()
    end
  end

  defp get_available_companies do
    case "production" do
      "production" ->
        # In production, only show specific companies
        production_companies = [
          %{market_key: :asx, listing_key: "mce"},
          %{market_key: :asx, listing_key: "hm1"},
          %{market_key: :asx, listing_key: "dre"}
        ]

        production_companies
        |> Enum.map(fn %{market_key: market_key, listing_key: listing_key} ->
          Companies.get_profile_by_market_listing(%{
            market_key: market_key,
            listing_key: listing_key
          })
        end)
        |> Enum.filter(& &1)

      _ ->
        # In development/local, show all companies
        ""
        |> Companies.search_company_profiles()
        |> Enum.take(20)
        |> Repo.preload(:ticker)
    end
  end
end
