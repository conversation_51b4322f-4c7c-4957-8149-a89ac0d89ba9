defmodule Gaia.Jobs.PublishMediaUpdate do
  @moduledoc """
  Publish media update
  """

  use Oban.Worker,
    queue: :publish_media,
    max_attempts: 2,
    priority: 0

  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Repo

  require Logger

  def enqueue(args, opts \\ []) do
    args
    |> new(opts)
    |> Oban.insert()
  end

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"media_id" => media_id} = args}) do
    Logger.info("Gaia.Jobs.PublishMediaUpdate for media #{media_id} started.")

    with %Media{media_update: %MediaUpdate{} = media_update} <-
           media_id
           |> Interactions.get_media()
           |> Repo.preload([:media_update]),
         :ok <-
           Interactions.update_or_publish_media_update(media_update, Map.get(args, "posted_by_id")) do
      Logger.info("Gaia.Jobs.PublishMediaUpdate for media #{media_id} finished.")
    else
      error ->
        Helper.Error.Custom.ErrorHandler.handle(
          "Error on publishing media update",
          %{media_id: media_id},
          error
        )

        Logger.info("Gaia.Jobs.PublishMediaUpdate for media #{media_id} errored.")
    end
  end
end
