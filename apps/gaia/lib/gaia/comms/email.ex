defmodule Gaia.Comms.Email do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  @fields [
    :campaign_name,
    :subject,
    :email_html,
    :email_json,
    :from_name,
    :from_email,
    :media_id,
    :invalidated,
    :last_updated_by,
    :scheduled_at,
    :is_draft,
    :sent_at,
    :company_profile_id,
    :is_welcome_email,
    :send_to_all_contacts,
    :send_to_static_list_ids,
    :send_to_dynamic_list_ids,
    :do_not_send_to_static_list_ids,
    :do_not_send_to_dynamic_list_ids,
    :send_to_contact_ids,
    :do_not_send_to_contact_ids
  ]

  @required_fields [
    :campaign_name,
    :invalidated,
    :is_draft,
    :company_profile_id,
    :send_to_all_contacts,
    # TAGS TODO: uncomment these fields when we are ready to use them in FE
    # :send_to_static_list_ids,
    :send_to_dynamic_list_ids,
    # :do_not_send_to_static_list_ids,
    :do_not_send_to_dynamic_list_ids,
    :send_to_contact_ids,
    :do_not_send_to_contact_ids
  ]

  @template_type [:custom, :default]
  @send_type [:one_off, :automated]
  @recipient_list_type [:all, :all_shareholders, :new_shareholders, :hub, :imported]

  def get_template_type, do: @template_type
  def get_send_type, do: @send_type
  def get_recipient_list_type, do: @recipient_list_type

  schema "comms_emails" do
    field(:campaign_name, :string)
    field(:from_email, :string)
    field(:from_name, :string)
    field(:invalidated, :boolean, default: false)
    field(:email_html, :string)
    field(:email_json, :string)
    field(:subject, :string)
    field(:is_draft, :boolean, default: true)
    field(:scheduled_at, :naive_datetime)
    field(:sent_at, :naive_datetime)
    field(:is_welcome_email, :boolean, default: false)
    field(:send_to_all_contacts, :boolean, default: false)
    field(:send_to_static_list_ids, {:array, :integer}, default: [])
    field(:send_to_dynamic_list_ids, {:array, :integer}, default: [])
    field(:do_not_send_to_static_list_ids, {:array, :integer}, default: [])
    field(:do_not_send_to_dynamic_list_ids, {:array, :integer}, default: [])
    field(:send_to_contact_ids, {:array, :integer}, default: [])
    field(:do_not_send_to_contact_ids, {:array, :integer}, default: [])

    @recipient_list_type [:all, :all_shareholders, :new_shareholders, :hub, :imported]
    # TODO: remove excluded_contacts, recipient_list_types, temp_flows_distributed_id
    field(:excluded_contacts, {:array, :id})
    field(:recipient_list_types, {:array, Ecto.Enum}, values: @recipient_list_type)

    # need to keep this field for analytics; see apps/gaia/lib/gaia/interactions.ex line 851 -> 854
    field(:temp_flows_distributed_id, :id)

    belongs_to(:company_profile, Gaia.Companies.Profile, foreign_key: :company_profile_id)
    belongs_to(:media, Gaia.Interactions.Media, foreign_key: :media_id)
    belongs_to(:last_updated_user, Gaia.Companies.ProfileUser, foreign_key: :last_updated_by)

    has_many(:email_recipients, Gaia.Comms.EmailRecipient, foreign_key: :email_id)
    has_many(:recipients_tracking_events, through: [:email_recipients, :tracking_email, :events])

    has_many(:email_unsubscribed_contacts, Gaia.Comms.ContactUnsubscribe, foreign_key: :unsubscribed_from_email_id)

    has_many(:email_globally_unsubscribed_contacts, Gaia.Comms.ContactGlobalUnsubscribe,
      foreign_key: :unsubscribed_from_email_id
    )

    timestamps()
  end

  @doc false
  def changeset(comms_email, attrs) do
    comms_email
    |> cast(attrs, @fields)
    |> validate_required(@required_fields)
    |> unique_constraint(:media_id, name: :unique_email_per_media_id)
    |> check_constraint(:send_to_all_contacts,
      name: :send_to_ids_must_be_empty_when_send_to_all_contacts_is_true
    )
    |> maybe_add_media_id()
  end

  # Auto-generates and assigns a `media_id` to the email changeset if none is provided.
  defp maybe_add_media_id(changeset) do
    now = NaiveDateTime.utc_now(:second)

    media_id = get_field(changeset, :media_id)
    campaign_name = get_field(changeset, :campaign_name)
    company_profile_id = get_field(changeset, :company_profile_id)
    last_updated_by = get_field(changeset, :last_updated_by)

    media_attrs = %{
      distribution_email_enabled: true,
      primary_distribution: "email",
      title: campaign_name,
      company_profile_id: company_profile_id,
      created_by_profile_user_id: last_updated_by,
      target_date: now,
      inserted_at: now,
      updated_at: now
    }

    if is_nil(media_id) do
      media_attrs
      |> Gaia.Interactions.create_media()
      |> case do
        {:ok, media} ->
          put_change(changeset, :media_id, media.id)

        {:error, _reason} ->
          add_error(changeset, :media_id, "failed to auto-generate media record")
      end
    else
      changeset
    end
  end
end
