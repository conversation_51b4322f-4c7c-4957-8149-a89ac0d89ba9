defmodule Gaia.ObanHelper do
  @moduledoc """
  Helper functions for Oban
  """
  import Ecto.Query, warn: false

  alias <PERSON><PERSON>.Jobs
  alias Gaia.Repo
  alias Helper.Error.Custom.ErrorHandler
  alias Jobs.PublishShareholderOffer
  alias Jobs.SendEmail

  # These values are in string because the value stored in meta.worker is in string
  # These are the list of workers to be excluded when sending error to monitoring tools
  @workers_to_ignore_error_reporting [
    "Gaia.Workers.ImportRNS"
  ]

  # Notification for critical jobs that failed after attempting max_attempts
  # Currently sending email and slack message to #dev-support
  @workers_to_send_critical_notification [
    "Gaia.Jobs.GenerateBaseWebsiteBuilderHubForCompany",
    "Gaia.Jobs.ImportAutomicRegister",
    "Gaia.Jobs.ImportBoardroomRegister",
    "Gaia.Jobs.ImportBoardroomRegistryCredential",
    "Gaia.Jobs.ImportLinkRegister",
    "Gaia.Jobs.ImportManualRegistry",
    "Gaia.Jobs.ImportXcendRegister",
    "Gaia.Jobs.SendEmail",
    # Only wanted the high priority error to be sent an email
    # Temporary fix and quite an anti pattern, check Gaia.Workers.ImportRNS
    "Gaia.Workers.ImportRNS"
  ]

  @doc """
  Sends error from Oban to AppSignal after attempting max_attempts.
  Sends email for critical jobs that failed after attempting max_attempts (Only register now).

  Reference: https://hexdocs.pm/oban/Oban.Telemetry.html#module-job-events

  We are only handling :exception event which includes:
  - When job returns :error or {:error, error}
  - When process exit caught
  - When an exception caught
  """
  # Wrap inside a try catch in case something went wrong to prevent telemetry from crashing
  def handle_error(
        [:oban, :job, :exception],
        _measure,
        %{attempt: attempt, max_attempts: max_attempts, worker: worker} = meta,
        _config
      )
      when attempt == max_attempts and worker not in @workers_to_ignore_error_reporting do
    send_critical_notification(meta)

    # Sends error to AppSignal
    exception = create_exception(meta)
    extra = create_extra(meta)
    ErrorHandler.capture_exception(exception, extra: extra)
  rescue
    e -> ErrorHandler.capture_exception(e)
  end

  # If error not happening on last attempt, skip error reporting
  def handle_error([:oban, :job, :exception], _measure, _meta, _config), do: :ok

  defp create_exception(%{worker: worker}) do
    %RuntimeError{message: "Error - #{worker}"}
  end

  # When job returns simple error tuple such as {:error, "simple error in string"}
  defp create_extra(%{args: args, error: %CaseClauseError{term: term}, id: id}) do
    Helper.Error.format_and_filter_data(%{args: args, error: term, oban_id: id})
  end

  # When job returns complex error tuple such as {:error, %{error: "404", message: "Forbidden"}}
  defp create_extra(%{args: args, error: %Oban.PerformError{reason: {:error, error}}, id: id}) do
    Helper.Error.format_and_filter_data(%{args: args, error: error, oban_id: id})
  end

  defp create_extra(%{args: args, error: error, id: id}) do
    Helper.Error.format_and_filter_data(%{args: args, error: error, oban_id: id})
  end

  def send_critical_notification(%{
        args: args,
        attempt: attempt,
        error: error,
        max_attempts: max_attempts,
        worker: worker
      })
      when attempt == max_attempts and worker in @workers_to_send_critical_notification do
    environment = String.upcase(Application.get_env(:helper, :runtime_env))

    message =
      "[#{environment}]\nFailed job:\n#{worker}\n\nJob arguments:\n#{inspect(args, pretty: true)}\n\nError:\n#{inspect(error, pretty: true)}"

    # Send email
    Gaia.Notifications.Email.deliver(EmailTransactional.Operations, :z_oban_worker_failed, [
      worker,
      message
    ])

    # Send to Slack #dev-support
    Slack.Message.send(%{text: message}, :dev_support_webhook_url)
  end

  def send_critical_notification(_meta) do
    :skip
  end

  def cancel_email_jobs(email_id) do
    jobs =
      Oban.Job
      |> where(fragment("args->>'email_id' = ?", ^"#{email_id}"))
      |> where([j], j.worker == "Gaia.Jobs.SendEmail")
      |> where([j], j.state == "scheduled")

    cancelled_jobs = Repo.all(jobs)

    Oban.cancel_all_jobs(jobs)

    cancelled_jobs
  end

  def reschedule_email_jobs([%Oban.Job{} | _] = jobs, _email_id, %NaiveDateTime{} = scheduled_at) do
    Task.start(fn ->
      Enum.each(jobs, &reschedule_email_job(&1, scheduled_at))
    end)

    :ok
  end

  def reschedule_email_jobs([], email_id, scheduled_at) do
    case SendEmail.enqueue(%{"email_id" => email_id},
           scheduled_at: DateTime.from_naive!(scheduled_at, "Etc/UTC")
         ) do
      {:ok, _} ->
        :ok

      error ->
        error
    end
  end

  defp reschedule_email_job(%{args: args}, %NaiveDateTime{} = scheduled_at) do
    SendEmail.enqueue(args, scheduled_at: DateTime.from_naive!(scheduled_at, "Etc/UTC"))
  end

  def cancel_shareholder_offer_jobs(shareholder_offer_id) do
    jobs =
      Oban.Job
      |> where(fragment("args->>'shareholder_offer_id' = ?", ^"#{shareholder_offer_id}"))
      |> where([j], j.worker == "Gaia.Jobs.PublishShareholderOffer")
      |> where([j], j.state == "scheduled")

    cancelled_jobs = Repo.all(jobs)

    Oban.cancel_all_jobs(jobs)

    cancelled_jobs
  end

  def reschedule_shareholder_offer_jobs([%Oban.Job{} | _] = jobs, _shareholder_offer_id, %NaiveDateTime{} = scheduled_at) do
    Task.start(fn ->
      Enum.each(jobs, &reschedule_shareholder_offer_job(&1, scheduled_at))
    end)

    :ok
  end

  def reschedule_shareholder_offer_jobs([], shareholder_offer_id, scheduled_at) do
    case PublishShareholderOffer.enqueue(%{"shareholder_offer_id" => shareholder_offer_id},
           scheduled_at: DateTime.from_naive!(scheduled_at, "Etc/UTC")
         ) do
      {:ok, _} ->
        :ok

      error ->
        error
    end
  end

  def active_workers_count(workers) do
    workers
    |> active_workers_count_query()
    |> Gaia.Repo.aggregate(:count)
  end

  def active_workers_count_by_meta(workers, meta) do
    workers
    |> active_workers_count_query()
    |> where([job], job.meta == ^meta)
    |> Gaia.Repo.aggregate(:count)
  end

  defp active_workers_count_query(workers) do
    Oban.Job
    |> where([job], job.worker in ^workers)
    |> where([job], job.state in ["executing", "scheduled", "available", "retryable"])
  end

  defp reschedule_shareholder_offer_job(%{args: args}, %NaiveDateTime{} = scheduled_at) do
    PublishShareholderOffer.enqueue(args,
      scheduled_at: DateTime.from_naive!(scheduled_at, "Etc/UTC")
    )
  end

  def cancel_media_distribution_jobs(media_id, distribution_worker) do
    jobs =
      Oban.Job
      |> where(fragment("args->>'media_id' = ?", ^"#{media_id}"))
      |> where([j], j.worker == ^distribution_worker)
      |> where([j], j.state == "scheduled")

    cancelled_jobs = Repo.all(jobs)

    Oban.cancel_all_jobs(jobs)

    cancelled_jobs
  end

  @doc """
  Check if the queue is empty.

  Can pass the option `exclude_id` to only check if there is any queued job apart from a specific id.
  This is useful if this function is called inside an Oban job.
  """
  def is_queue_empty?(queue, opts \\ [])

  def is_queue_empty?(queue, opts) do
    Oban.Job
    |> where(queue: ^"#{queue}")
    |> where([oj], oj.state in ["available", "executing", "retryable", "scheduled"])
    |> maybe_exclude_id(Keyword.get(opts, :exclude_id))
    |> Gaia.Repo.exists?()
    |> Kernel.not()
  end

  defp maybe_exclude_id(query, nil), do: query
  defp maybe_exclude_id(query, id), do: where(query, [q], q.id != ^id)
end
