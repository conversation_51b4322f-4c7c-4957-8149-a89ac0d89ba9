defmodule Gaia.Admins.AgentQuestion do
  @moduledoc """
  Schema for admin agent questions - feedback and feature requests for AI agents
  """
  use Ecto.Schema

  import Ecto.Changeset

  @type t :: %__MODULE__{
          id: integer(),
          question: String.t(),
          reason: String.t() | nil,
          is_done: boolean(),
          admin_user_id: integer(),
          admin_user: Gaia.Admins.User.t() | Ecto.Association.NotLoaded.t(),
          inserted_at: NaiveDateTime.t(),
          updated_at: NaiveDateTime.t()
        }

  schema "admin_agents_questions" do
    field :question, :string
    field :reason, :string
    field :is_done, :boolean, default: false

    belongs_to :admin_user, Gaia.Admins.User

    timestamps()
  end

  @doc """
  Changeset for creating a new agent question
  """
  def changeset(agent_question, attrs) do
    agent_question
    |> cast(attrs, [:question, :reason, :is_done, :admin_user_id])
    |> validate_required([:question, :admin_user_id])
    |> validate_length(:question, min: 10, max: 1000)
    |> validate_length(:reason, max: 1000)
    |> foreign_key_constraint(:admin_user_id)
  end

  @doc """
  Changeset for updating an existing agent question
  """
  def update_changeset(agent_question, attrs) do
    agent_question
    |> cast(attrs, [:question, :reason, :is_done])
    |> validate_required([:question])
    |> validate_length(:question, min: 10, max: 1000)
    |> validate_length(:reason, max: 1000)
  end
end
