defmodule Gaia.Interactions do
  @moduledoc """
  The Interactions context.
  """

  use Helper.Pipe

  import Ecto.Query, warn: false
  import Helper.Guard, only: [is_id?: 1]

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Companies.SocialConnection
  alias Gaia.Flows
  alias Gaia.Flows.Distribution
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Interactions.DuplicateMedia
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaComment
  alias Gaia.Interactions.MediaCommentLike
  alias Gaia.Interactions.MediaCommentRead
  alias Gaia.Interactions.MediaCommentStar
  alias Gaia.Interactions.MediaLike
  alias Gaia.Interactions.MediaQueries
  alias Gaia.Interactions.MediaSurveyAnswer
  alias Gaia.Interactions.MediaTag
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Interactions.MediaUpdateAttachment
  alias Gaia.Interactions.MediaUpdateContent
  alias Gaia.Interactions.PreparedAnnouncement
  alias Gaia.Interactions.SocialPost
  alias Gaia.Investors.User
  alias Gaia.Markets.Ticker
  alias Gaia.Raises.RaisesSppHistorical
  alias Gaia.Repo
  alias Gaia.Socials.LinkedIn
  alias Gaia.Socials.Twitter
  alias Gaia.Tracking.InvestorHub
  alias Helper.Error.Custom.ErrorHandler

  require Helper.Error.Custom.ErrorHandler
  require Logger

  @doc """
  Returns the list of interactions_medias.

  ## Examples

      iex> list_interactions_medias()
      [%Media{}, ...]

  """
  def list_interactions_medias do
    Repo.all(Media)
  end

  @doc """
  Gets a single media.

  Raises `Ecto.NoResultsError` if the Media does not exist.

  ## Examples

      iex> get_media!(123)
      %Media{}

      iex> get_media!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media!(id), do: Repo.get!(Media, id)

  @doc """
  Gets a single media.

  Returns `nil` if the Media does not exist.

  ## Examples

      iex> get_media(123)
      %Media{}

      iex> get_media(456)
      nil

  """
  def get_media(id), do: Repo.get(Media, id)
  def get_media_by(opts), do: Repo.get_by(Media, opts)

  def list_media_by_company_profile_id(company_profile_id) do
    Media
    |> where([media], media.company_profile_id == ^company_profile_id)
    |> Repo.all()
  end

  @doc """
  Creates a media.

  ## Examples

      iex> create_media(%{field: value})
      {:ok, %Media{}}

      iex> create_media(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media(attrs \\ %{}) do
    %Media{}
    |> Media.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media.

  ## Examples

      iex> update_media(media, %{field: new_value})
      {:ok, %Media{}}

      iex> update_media(media, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media(%Media{} = media, attrs) do
    media
    |> Media.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media.

  ## Examples

      iex> delete_media(media)
      {:ok, %Media{}}

      iex> delete_media(media)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media(%Media{} = media) do
    Repo.delete(media)
  end

  @doc """
  Duplicates a media item and all its associated distributions.

  ## Examples

      iex> duplicate_media(media)
      {:ok, %Media{}}

      iex> duplicate_media(media)
      {:error, %Ecto.Changeset{}}

  """
  def duplicate_media(%Media{} = media) do
    DuplicateMedia.duplicate_media(media)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media changes.

  ## Examples

      iex> change_media(media)
      %Ecto.Changeset{data: %Media{}}

  """
  def change_media(%Media{} = media, attrs \\ %{}) do
    Media.changeset(media, attrs)
  end

  def invalidate_media(%Media{id: media_id} = media) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:media, change_media(media, %{invalidated: true}))
    |> Ecto.Multi.update_all(
      :media_comment,
      from(m in MediaComment, where: m.media_id == ^media_id),
      set: [invalidated: true]
    )
    |> Ecto.Multi.update_all(
      :media_update,
      from(m in MediaUpdate, where: m.media_id == ^media_id),
      set: [invalidated: true]
    )
    |> Repo.transaction()
  end

  def medias_query(options) do
    MediaQueries.medias_query(options)
  end

  def sort_content_calendar(
        current_company_profile_id,
        source_year,
        source_month,
        source_media_ids,
        target_year,
        target_month,
        target_media_ids
      ) do
    # Convert IDs to integers
    source_media_ids = Enum.map(source_media_ids, &String.to_integer/1)
    target_media_ids = Enum.map(target_media_ids, &String.to_integer/1)

    # Create source and target dates
    source_date_utc =
      if source_year && source_month do
        date = Date.new!(source_year, source_month, 1)
        DateTime.new!(date, ~T[00:00:00], "Etc/UTC")
      end

    target_date_utc =
      if target_year && target_month do
        date = Date.new!(target_year, target_month, 1)
        DateTime.new!(date, ~T[00:00:00], "Etc/UTC")
      end

    # These variables are no longer needed since we're using target_date now
    _source_date =
      if source_year && source_month do
        Date.new!(source_year, source_month, 1)
      end

    _target_date =
      if target_year && target_month do
        Date.new!(target_year, target_month, 1)
      end

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(
      :update_source,
      from(m in Media,
        where: m.company_profile_id == ^current_company_profile_id,
        where: m.id in ^source_media_ids
      ),
      set: [
        target_date: source_date_utc
      ]
    )
    |> Ecto.Multi.update_all(
      :update_target,
      from(m in Media,
        where: m.company_profile_id == ^current_company_profile_id,
        where: m.id in ^target_media_ids
      ),
      set: [
        target_date: target_date_utc
      ]
    )
    |> Repo.transaction()
  end

  @doc """
  Returns the list of interactions_media_announcements.

  ## Examples

      iex> list_interactions_media_announcements()
      [%MediaAnnouncement{}, ...]

  """
  def list_interactions_media_announcements do
    Repo.all(MediaAnnouncement)
  end

  @doc """
  Returns the list of interactions_media_announcements given a company_profile_id.

  ## Examples

      iex> list_interactions_media_announcements(1, 100)
      [%MediaAnnouncement{}, ...]

  """
  def list_interactions_media_announcements(company_profile_id, limit \\ 10) do
    limit = min(100, limit)

    Repo.all(
      from(ma in MediaAnnouncement,
        join: me in assoc(ma, :media),
        where: me.invalidated == false,
        where: me.company_profile_id == ^company_profile_id,
        order_by: [desc: ma.id],
        limit: ^limit
      )
    )
  end

  @doc """
  Gets a single media_announcement.

  Raises `Ecto.NoResultsError` if the Media announcement does not exist.

  ## Examples

      iex> get_media_announcement!(123)
      %MediaAnnouncement{}

      iex> get_media_announcement!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_announcement!(id), do: Repo.get!(MediaAnnouncement, id)

  @doc """
  Gets a single media_announcement.

  Returns `nil` if the Media announcement does not exist.

  ## Examples

      iex> get_media_announcement(123)
      %MediaAnnouncement{}

      iex> get_media_announcement(456)
      nil

  """
  def get_media_announcement(id) do
    MediaAnnouncement
    |> join(:left, [ma], m in assoc(ma, :media))
    |> preload([_, m], media: m)
    |> Repo.get(id)
  end

  @doc """
  Gets a single media_announcement.

  Returns `nil` if the Media announcement does not exist.
  Or if the id is not an integer or a string that can be converted to an integer.
  Getting lots of erorrs of people getting announcements by slugs that don't exist.

    ## Examples

      iex> get_media_announcement_by_id(123)
      %MediaAnnouncement{}

      iex> get_media_announcement_by_id("123")
      %MediaAnnouncement{}

      iex> get_media_announcement_by_id("abc")
      nil

  """
  def get_media_announcement_by_id(id) when is_integer(id) do
    exec_get_media_announcement_by_id(id)
  end

  def get_media_announcement_by_id(id) when is_binary(id) do
    case Integer.parse(id) do
      {int_id, ""} -> exec_get_media_announcement_by_id(int_id)
      _ -> nil
    end
  end

  def get_media_announcement_by_id(_), do: nil

  defp exec_get_media_announcement_by_id(id) do
    MediaAnnouncement
    |> join(:left, [ma], m in assoc(ma, :media))
    |> preload([_, m], media: m)
    |> Repo.get(id)
  end

  @doc """
  Gets a single media_announcement.

  Returns `nil` if the Media announcement does not exist.

  ## Examples

      iex> get_media_announcement_by(header: "Exist")
      %MediaAnnouncement{}

      iex> get_media_announcement_by(header: "Unknown")
      nil

  """
  def get_media_announcement_by(opts), do: Repo.get_by(MediaAnnouncement, opts)

  @doc """
  Creates a media_announcement.

  ## Examples

      iex> create_media_announcement(%{field: value})
      {:ok, %MediaAnnouncement{}}

      iex> create_media_announcement(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_announcement(attrs \\ %{}) do
    %MediaAnnouncement{}
    |> MediaAnnouncement.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_announcement.

  ## Examples

      iex> update_media_announcement(media_announcement, %{field: new_value})
      {:ok, %MediaAnnouncement{}}

      iex> update_media_announcement(media_announcement, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_announcement(%MediaAnnouncement{} = media_announcement, attrs) do
    media_announcement
    |> MediaAnnouncement.changeset(attrs)
    |> Repo.update()
  end

  # Manually add media_announcement pdf and text
  def update_media_announcement_pdf_and_text(%MediaAnnouncement{} = media_announcement, attrs) do
    media_announcement
    |> MediaAnnouncement.update_media_announcement_pdf_and_text_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_announcement.

  ## Examples

      iex> delete_media_announcement(media_announcement)
      {:ok, %MediaAnnouncement{}}

      iex> delete_media_announcement(media_announcement)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_announcement(%MediaAnnouncement{} = media_announcement) do
    Repo.delete(media_announcement)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_announcement changes.

  ## Examples

      iex> change_media_announcement(media_announcement)
      %Ecto.Changeset{data: %MediaAnnouncement{}}

  """
  def change_media_announcement(%MediaAnnouncement{} = media_announcement, attrs \\ %{}) do
    MediaAnnouncement.changeset(media_announcement, attrs)
  end

  def import_announcements_for_time_period(shift_options \\ [months: -3]) do
    now = Timex.to_date(Helper.ExDay.now())
    time_ago = Timex.shift(now, shift_options)

    dates =
      [from: time_ago, until: now]
      |> Timex.Interval.new()
      |> Timex.Interval.with_step(days: 1)
      |> Enum.map(&Timex.to_date(&1))

    Enum.map(dates, &import_media_announcements_by_date(&1))
  end

  def import_media_announcements_by_date(date) do
    with {:ok, token} <- Weblink.get_weblink_token(),
         formatted_date = Timex.format!(date, "%Y%m%d", :strftime),
         {:ok, weblink_announcements} <-
           Weblink.get_announcements_by_date(formatted_date, token) do
      import_new_media_announcements_exec(weblink_announcements)
    end
  end

  def import_new_media_announcements_for_asx do
    with {:ok, token} <- Weblink.get_weblink_token(),
         {:ok, weblink_announcements} <-
           Helper.ExDay.now()
           |> Timex.format!("%Y%m%d", :strftime)
           |> Weblink.get_announcements_by_date(token) do
      import_new_media_announcements_exec(weblink_announcements)
    end
  end

  defp import_new_media_announcements_exec([]), do: :ok

  # If updating functionality here, please update in duplicated function for testing below, ensuring return type of duplicate remains the same.
  defp import_new_media_announcements_exec([weblink_announcement | weblink_announcements]) do
    case create_new_media_announcement(weblink_announcement) do
      {:ok, %{media_announcement: %MediaAnnouncement{} = media_announcement}} ->
        on_announcement_created(media_announcement)

      _ ->
        nil
    end

    import_new_media_announcements_exec(weblink_announcements)
  end

  def test_announcement_import_duplicate_function(weblink_format_announcement) do
    case create_new_media_announcement(weblink_format_announcement) do
      {:ok, %{media_announcement: %MediaAnnouncement{} = media_announcement}} ->
        on_announcement_created(media_announcement)

      error ->
        error
    end
  end

  # Functions to be executed when announcement is created
  def on_announcement_created(%MediaAnnouncement{id: id, url: url, market_key: market_key} = media_announcement) do
    # Only generate announcement thumbnail when it has media_id
    # Announcement with media_id also means the company is our client
    # Generating announcement thumbnail is slow, so decided to optimise our resource usage
    unless is_nil(media_announcement.media_id) or
             MediaAnnouncement.url_is_invalid?(url) do
      Gaia.Jobs.CreateAnnouncementThumbnail.enqueue(%{announcement_id: id, url: url})
    end

    Application.get_env(:helper, :runtime_env) == "production" &&
      Task.start(__MODULE__, :send_media_announcement_slack_notification, [
        media_announcement
      ])

    Task.start(fn ->
      Gaia.Flows.Distribution.enqueue_announcement_distributions(media_announcement)
    end)

    # Sending a version of the notificaiton that now highlights the AI summary feature
    # https://www.figma.com/design/Nj2pLMHbjzV8xCf5gZaVaO/AI-Generated-Summaries?node-id=671-5647&t=VUqTIIWQK5s1Ox3j-1
    Task.start(fn ->
      send_announcement_notification_ai_summary_email_to_profile_admins(media_announcement)
    end)

    # Disabling this for now in favour of AI summaries email
    # Task.start(fn ->
    #   send_automate_announcements_email_to_profile_admins(media_announcement)
    # end)

    # Vectorize the announcement content only for companies with smart_search flag
    if market_key == "ASX" and not is_nil(media_announcement.media_id) do
      # Get the company profile from the media_id
      company_profile =
        media_announcement.media_id
        |> Gaia.Interactions.get_media!()
        |> Map.get(:company_profile_id)
        |> Gaia.Companies.get_profile()

      # Check if the smart_search feature flag is enabled for this company profile
      if Gaia.FeatureFlags.enabled?(:smart_search, for: company_profile) do
        Task.start(fn ->
          Gaia.Vector.create_announcement_content_from_announcement_id(id)
        end)
      end
    end

    {:ok, media_announcement}
  end

  # Create ASX announcement from weblink format + test announcements in Hades
  def create_new_media_announcement(
        %{
          "categoryId" => category_id,
          "date" => date,
          "headlineText" => headline_test,
          "marketSensitive" => market_sensitive,
          "pdfLink" => pdf_link,
          "symbol" => symbol,
          "time" => time
        } = announcement_params
      ) do
    posted_at = get_media_announcement_posted_at("#{date}-#{time}")

    market_key = announcement_params["marketKey"] || :asx

    ticker =
      Gaia.Markets.get_ticker_by(%{
        market_key: market_key,
        listing_key: String.downcase(symbol)
      })

    case get_media_announcement_by(%{url: pdf_link}) do
      nil ->
        Logger.info("Inserting announcement: #{inspect(%{listing_key: symbol, posted_at: posted_at, url: pdf_link})}")

        try do
          multi =
            Ecto.Multi.new()
            |> multi_insert_media(ticker)
            |> Ecto.Multi.insert(:media_announcement, fn changes ->
              change_media_announcement(%MediaAnnouncement{}, %{
                header: headline_test,
                listing_key: symbol,
                market_key: market_key |> Atom.to_string() |> String.upcase(),
                market_sensitive: market_sensitive === "Y",
                media_id: changes |> Map.get(:media, %{}) |> Map.get(:id),
                posted_at: posted_at,
                rectype:
                  category_id
                  |> String.split(",")
                  |> List.first()
                  |> MediaAnnouncement.get_rectype_from_subtype(market_key),
                subtypes: String.split(category_id, ","),
                url: pdf_link,
                seen_at: NaiveDateTime.utc_now(:second)
              })
            end)
            |> Repo.transaction(timeout: 80_000)
            |> case do
              {:ok, %{media_announcement: media_announcement} = multi} ->
                # Running demo functions async, also don't care if they fail
                Task.async(fn -> run_demo_functions(media_announcement) end)

                {:ok, multi}

              # Because we are running in cron so might have racing issues. It is an expected bug so can skip to appsignal
              {:error, :media_announcement,
               %Ecto.Changeset{
                 errors: [
                   url: {"has already been taken", _}
                 ]
               } = error} ->
                {:error, error}

              {:error, error} ->
                log_announcement_error(error, symbol, pdf_link, posted_at)
                {:error, error}
            end

          # ^increasing timeout as converting pdf to html / text can take a couple seconds

          multi
        rescue
          e ->
            log_announcement_error(e, symbol, pdf_link, posted_at)
            ErrorHandler.capture_exception(e)
        end

      _ ->
        :skip
    end
  end

  # AQSE/LSE from RNS
  def create_new_media_announcement(%{
        title: title,
        body_content: _,
        publication_time: _,
        received_time: received_time,
        publish_reason: _,
        market_key: market_key,
        listing_key: listing_key,
        news_product: news_product,
        public_identifier: _,
        rns_category: rns_category,
        rns_number: _,
        rns_ann_id: _,
        pdf_url: pdf_url
      }) do
    ticker =
      Gaia.Markets.get_ticker_by(%{
        market_key: String.downcase(market_key),
        listing_key: String.downcase(listing_key)
      })

    announcement_attrs = %{
      header: title,
      listing_key: listing_key,
      market_key: market_key,
      # RNS have no concept of market sensitive
      market_sensitive: false,
      posted_at: received_time,
      news_publisher:
        case news_product do
          "RNSRegulatory" -> "RNS"
          "RNSNonregulatory" -> "Reach"
        end,
      rectype: MediaAnnouncement.get_rectype_from_subtype(rns_category, market_key),
      subtypes:
        case rns_category do
          nil -> []
          value -> [value]
        end,
      url: pdf_url,
      seen_at: NaiveDateTime.utc_now(:second)
    }

    case get_media_announcement_by(%{url: pdf_url}) do
      nil ->
        Logger.info(
          "Inserting announcement: #{inspect(%{listing_key: listing_key, posted_at: received_time, url: pdf_url})}"
        )

        # Increasing timeout as converting pdf to html / text can take a couple seconds
        Ecto.Multi.new()
        |> multi_insert_media(ticker)
        |> Ecto.Multi.insert(:media_announcement, fn changes ->
          # Attach media_id to new announcement
          new_announcement_attrs =
            Enum.into(
              %{media_id: changes |> Map.get(:media, %{}) |> Map.get(:id)},
              announcement_attrs
            )

          change_media_announcement(%MediaAnnouncement{}, new_announcement_attrs)
        end)
        |> Ecto.Multi.run(:demo_functions, fn _repo, %{media_announcement: media_announcement} = _changes ->
          run_demo_functions(media_announcement)
        end)
        |> Repo.transaction(timeout: 50_000)

      %MediaAnnouncement{} = existing_announcement ->
        # On the RNS / Moody's there is a concept of replacement
        # So far, there is two types of replacement
        # 1. Treated as same announcement where the RNS number is the same
        # 2. Treated as different announcement where the RNS number is different
        # This update is only catering for scenario 1. where the pdf_url is the same as the original file
        Logger.info(
          "Updating announcement: #{inspect(%{listing_key: listing_key, posted_at: received_time, url: pdf_url})}"
        )

        Ecto.Multi.new()
        |> Ecto.Multi.update(
          :media_announcement,
          change_media_announcement(existing_announcement, announcement_attrs)
        )
        |> Repo.transaction(timeout: 50_000)
    end
  end

  def create_new_media_announcement(_), do: :skip

  defp multi_insert_media(multi, %Gaia.Markets.Ticker{company_profile_id: company_profile_id})
       when not is_nil(company_profile_id) do
    Ecto.Multi.insert(
      multi,
      :media,
      change_media(%Media{}, %{company_profile_id: company_profile_id})
    )
  end

  defp multi_insert_media(multi, _), do: multi

  defp get_media_announcement_posted_at(datetime) do
    case Timex.parse(datetime, "{YYYY}{0M}{0D}-{h24}{m}{s}") do
      {:ok, posted_at} ->
        posted_at
        |> Timex.to_datetime("Australia/Sydney")
        |> Timex.to_naive_datetime()

      _ ->
        nil
    end
  end

  def maybe_send_249d_announcement_slack_notification(%MediaAnnouncement{
        url: url,
        header: header,
        listing_key: listing_key,
        market_key: market_key
      }) do
    if header |> String.downcase() |> String.contains?("249d") do
      Slack.Message.send(
        %{
          attachments: [
            %Slack.Message{
              title: header,
              title_link: url,
              text: "Ticker: #{market_key}:#{listing_key}}"
            }
          ]
        },
        :announcement_249d_notifier_webhook_url
      )
    end
  end

  # Only send slack notification when market_key is ASX
  def send_media_announcement_slack_notification(
        %MediaAnnouncement{
          id: id,
          rectype: rectype,
          header: header,
          listing_key: listing_key,
          market_key: "ASX" = market_key
        } = media_announcement
      ) do
    listing_keys = Gaia.Markets.get_all_existing_client_listing_keys(market_key)
    maybe_send_249d_announcement_slack_notification(media_announcement)

    with true <- String.downcase(listing_key) in listing_keys,
         base_url when is_binary(base_url) <-
           Helper.InvestorHub.get_base_url(
             %{market_key: market_key, listing_key: listing_key}
             |> Gaia.Markets.get_ticker_by()
             |> Ticker.resolve_market_listing_key()
           ),
         :ok <-
           Slack.Message.send(
             %{
               attachments: [
                 %Slack.Message{
                   title: "#{market_key}:#{listing_key}: #{header}",
                   title_link: "#{base_url}/announcements/#{id}",
                   text: "Type: #{MediaAnnouncement.get_media_announcement_type_from_rectype(rectype, market_key)}"
                 }
               ]
             },
             :announcement_notifier_webhook_url
           ) do
      Gaia.GPT.maybe_send_ai_summary(media_announcement)
      :ok
    else
      false ->
        :nothing

      error ->
        Helper.Error.Custom.ErrorHandler.handle_error(
          "Error on sending media announcement slack notification",
          %{
            announcement_id: media_announcement.id
          },
          error
        )

        :error
    end
  end

  def send_media_announcement_slack_notification(_), do: :nothing

  def send_announcement_notification_ai_summary_email_to_profile_admins(
        %MediaAnnouncement{media: %Ecto.Association.NotLoaded{}} = media_announcement
      ) do
    media_announcement
    |> Repo.preload(:media)
    |> send_announcement_notification_ai_summary_email_to_profile_admins()
  end

  def send_announcement_notification_ai_summary_email_to_profile_admins(
        %MediaAnnouncement{
          media: %Media{company_profile_id: company_profile_id},
          market_key: market_key,
          subtypes: subtypes
        } = media_announcement
      ) do
    subtypes
    |> Enum.any?(&(&1 in Gaia.Interactions.MediaAnnouncement.get_recommended_type_values(market_key)))
    |> if do
      company_profile_id
      |> Gaia.Companies.active_admin_users_for_distribution_email(:announcement)
      |> Enum.each(
        &deliver_announcement_ai_summary_notification(
          [
            &1,
            media_announcement
          ],
          company_profile_id
        )
      )
    else
      :ok
    end
  end

  # Announcements for company that are not on InvestorHub won't have media_id
  # Do not send out these emails
  def send_announcement_notification_ai_summary_email_to_profile_admins(_), do: :skip

  defp deliver_announcement_ai_summary_notification(
         [%Gaia.Companies.ProfileUser{} = profile_user, _] = params,
         company_profile_id
       ) do
    EmailTransactional.Company
    |> Gaia.Notifications.Email.deliver_with_permission_check(
      :announcement_posted_ai_version,
      params,
      profile_user,
      "interactions_media_announcements.editor",
      company_profile_id
    )
    |> case do
      {:ok, _} ->
        :ok

      error ->
        ErrorHandler.capture_exception(error)
        :ok
    end
  end

  def send_automate_announcements_email_to_profile_admins(
        %MediaAnnouncement{media: %Ecto.Association.NotLoaded{}} = media_announcement
      ) do
    media_announcement
    |> Repo.preload(:media)
    |> send_automate_announcements_email_to_profile_admins()
  end

  def send_automate_announcements_email_to_profile_admins(
        %MediaAnnouncement{media: %Media{company_profile_id: company_profile_id}, market_key: market_key} =
          media_announcement
      ) do
    %{
      some_configured: some_configured,
      email_campaign_status: email_campaign_status,
      linkedin_post_status: linkedin_post_status,
      twitter_post_status: twitter_post_status
    } = get_configurations_for_media(company_profile_id, media_announcement)

    has_content =
      (media_announcement.summary || media_announcement.social_video_url ||
         media_announcement.video_url) != nil

    if some_configured or has_content do
      company_profile_id
      |> Gaia.Companies.active_admin_users_for_distribution_email(:announcement)
      |> Enum.each(
        &deliver_automate_notification(
          [
            &1,
            media_announcement,
            email_campaign_status,
            linkedin_post_status,
            twitter_post_status,
            some_configured,
            has_content
          ],
          company_profile_id
        )
      )
    else
      send_should_configure_distribution_email(
        company_profile_id,
        media_announcement,
        email_campaign_status,
        linkedin_post_status,
        twitter_post_status,
        some_configured,
        has_content,
        market_key
      )
    end
  end

  # Announcements for company that are not on InvestorHub won't have media_id
  # Do not send out these emails
  def send_automate_announcements_email_to_profile_admins(_), do: :skip

  defp deliver_automate_notification(params, company_profile_id) do
    EmailTransactional.Company
    |> Gaia.Notifications.Email.deliver(
      :announcement_posted,
      params,
      company_profile_id
    )
    |> case do
      {:ok, _} ->
        :ok

      error ->
        ErrorHandler.capture_exception(error)
        :ok
    end
  end

  # We have decided to only send emails to admins when the announcement type is in our recommended list.
  defp send_should_configure_distribution_email(
         company_profile_id,
         %{subtypes: subtypes} = media_announcement,
         email_campaign_status,
         linkedin_post_status,
         twitter_post_status,
         some_configured,
         has_content,
         market_key
       ) do
    subtypes
    |> Enum.any?(&(&1 in Gaia.Interactions.MediaAnnouncement.get_recommended_type_values(market_key)))
    |> if do
      company_profile_id
      |> Gaia.Companies.active_admin_users_for_distribution_email(:announcement)
      |> Enum.each(
        &deliver_configure_notification(
          [
            &1,
            media_announcement,
            email_campaign_status,
            linkedin_post_status,
            twitter_post_status,
            some_configured,
            has_content
          ],
          company_profile_id
        )
      )
    else
      :ok
    end
  end

  defp deliver_configure_notification(params, company_profile_id) do
    EmailTransactional.Company
    |> Gaia.Notifications.Email.deliver(
      :announcement_posted,
      params,
      company_profile_id
    )
    |> case do
      {:ok, _} ->
        :ok

      error ->
        ErrorHandler.capture_exception(error)
        :ok
    end
  end

  def count_total_distributed_campaign_views_for_media(media_id) do
    # why does this logic exist?
    # because the utm urls are already in the wild and we need to track them (new events may still yet come in from these old urls)
    # so even though we have migrated all flows_distributed_emails to comms_emails, we still have to track the old flows_distributed_email_ids used in old utm urls for announcement distribution prior to the migration

    # deid should always have email since the migration from flows_distributed_emails to comms_emails did not have a count mismatch at the end of the transaction
    # the default case is for completeness, it should never be hit
    %{media_id: media_id}
    |> Comms.get_email_by()
    |> case do
      %Email{
        company_profile_id: company_profile_id,
        temp_flows_distributed_id: temp_flows_distributed_id,
        id: id
      } ->
        deid_to_use = temp_flows_distributed_id || id

        Gaia.Tracking.count_company_events_with_metadata(
          company_profile_id,
          "utm_event",
          %{
            "utm_medium" => "email",
            "utm_source" => "distribution",
            "utm_campaign" => "deid-#{deid_to_use}"
          }
        )

      _ ->
        0
    end
  end

  def count_prepared_link_views_for_media_announcement(%MediaAnnouncement{id: announcement_id, media_id: media_id}) do
    %{media_id: media_id}
    |> get_prepared_announcement_by()
    |> case do
      %PreparedAnnouncement{company_profile_id: company_profile_id} ->
        Gaia.Tracking.count_company_events_with_metadata(
          company_profile_id,
          "link_redirect",
          %{
            "announcement_id" => to_string(announcement_id)
          }
        )

      _ ->
        0
    end
  end

  @doc """
  This function is to see if company has setup automated distribution for a specific announcement or update
  """
  def get_configurations_for_media(company_profile_id, media) do
    media_type =
      cond do
        is_struct(media, MediaAnnouncement) ->
          :announcement

        is_struct(media, MediaUpdate) ->
          :update

        true ->
          raise "Invalid input type for media for function get_configurations/2"
      end

    # Get active distribution settings for the company
    settings =
      Flows.get_active_settings_by_flow_for_company(
        media_type,
        company_profile_id
      )

    email_setting = Enum.find(settings, &(&1.channel == :email))
    linkedin_setting = Enum.find(settings, &(&1.channel == :linkedin))
    twitter_setting = Enum.find(settings, &(&1.channel == :twitter))

    email_setting_configured = setting_configured?(email_setting, media)
    linkedin_setting_configured = setting_configured?(linkedin_setting, media)
    twitter_setting_configured = setting_configured?(twitter_setting, media)

    # Check if social has been setted up
    social_connection =
      Companies.get_social_connection_by(%{
        company_profile_id: company_profile_id
      })

    linkedin_setup_completed = Companies.get_is_linkedin_setup_completed(social_connection)

    twitter_setup_completed = Companies.get_is_twitter_setup_completed(social_connection)

    # Returns
    %{
      email_setting_configured: email_setting_configured,
      linkedin_setting_configured: linkedin_setting_configured,
      twitter_setting_configured: twitter_setting_configured,
      some_configured:
        email_setting_configured or linkedin_setting_configured or
          twitter_setting_configured,
      email_campaign_status:
        if email_setting_configured do
          "automatic"
          # We didn't need to use rectype here because we didn't save rectype in the settings
          #  if link has not been changed, use investor hub url as default
          #  if link has not been changed, use investor hub url as default
          #  if link has not been changed, use investor hub url as default
          #  if link has not been changed, use investor hub url as default

          # credo:disable-for-lines:50

          # TODO will need to add for socials when time comes

          # Case where no comments were found in the query

          # Only returns investor questions => changed to return both company & investor comments/questions

          # TODO: Rename announcement_comment_id.

          # TODO: Rename announcement_comment_id.

          # Need poppler utility for this
          # brew install poppler

          # Above gets the "hrefs" from real links
          # Also need to get plain text links

          # Remove tmp folder so as not to clog device space

          # Remove tmp folder so as not to clog device space

          # Note: a better way to do this might be to find all active prepped announcements, and search for the presence
          # of their hashed ids in each imported announcement.  This would be simpler, but this way is working for now.

          # Remove all non-ASCII characters

          # Find index of magic link in the text

          # Grab just the 5 characters after the link text, which should 🤞 be the hashed id of the prepped announcement

          # announcements_list includes both media_announcements and prepared_announcements
          # Wait max 30 seconds for conversion so as not to overload server
          # Wait max 30 seconds for conversion so as not to overload server

          # only added pdf_text for now
          # |> where([ma], is_nil(ma.pdf_as_html) or is_nil(ma.pdf_as_text))
          # only added pdf_text for now

          # html = convert_pdf_to_html(url)
          # update_media_announcement_pdf_and_text(announcement, %{pdf_as_html: html, pdf_as_text: text})
        else
          "manual"
        end,
      linkedin_post_status:
        get_social_post_status(
          linkedin_setting_configured,
          linkedin_setup_completed
        ),
      twitter_post_status:
        get_social_post_status(
          twitter_setting_configured,
          twitter_setup_completed
        )
    }
  end

  # Disable demo functions for now
  defp run_demo_functions(_media_announcement) do
    # with {:ok, true} <-
    #        Gaia.DemoHelper.is_demo_market_listing_key(
    #          media_announcement.market_key,
    #          media_announcement.listing_key
    #        ) do
    #   Gaia.DemoHelper.add_ai_summary(media_announcement)
    #   Gaia.DemoHelper.add_ai_qa(media_announcement)
    #   Gaia.DemoHelper.add_random_likes(media_announcement)
    #   Gaia.DemoHelper.add_random_survey(media_announcement)
    #   Gaia.DemoHelper.add_random_announcement_views(media_announcement)
    # end

    {:ok, true}
  end

  defp setting_configured?(
         %DistributionSettings{included_announcement_types: included_announcement_types},
         %MediaAnnouncement{subtypes: subtypes}
       )
       when is_list(included_announcement_types) and is_list(subtypes) do
    Enum.any?(subtypes, &Enum.member?(included_announcement_types, &1))
  end

  defp setting_configured?(%DistributionSettings{included_update_types: included_update_types}, %MediaUpdate{
         included_types: included_types
       })
       when is_list(included_update_types) and is_list(included_types) do
    Enum.any?(included_update_types, &(&1 in included_types))
  end

  defp setting_configured?(_distribution_setting, _), do: false

  defp get_social_post_status(true, true), do: "automatic"
  defp get_social_post_status(false, true), do: "manual"

  defp get_social_post_status(_dist_configured, _setup_completed), do: "not connected"

  def get_distribution_contacts(company_profile_id, [_ | _] = recipient_list_type, excluded_contacts, unsubscriber_scope)
      when is_list(excluded_contacts) do
    company_profile_id
    |> Gaia.Contacts.contacts_query(%{
      filters: [
        %{
          key: "campaign_email_audience_list",
          value:
            Enum.join(
              [
                unsubscriber_scope
                | Enum.map(recipient_list_type, &String.upcase(Atom.to_string(&1)))
              ],
              ","
            )
        },
        %{key: "has_email", value: "true"}
      ]
    })
    |> subquery()
    |> distinct([c], c.id)
    |> Gaia.Repo.all()
    |> Enum.filter(&(&1.id not in excluded_contacts))
  end

  def get_distribution_contacts(company_profile_id, [_ | _] = recipient_list_type, _, unsubscriber_scope) do
    company_profile_id
    |> Gaia.Contacts.contacts_query(%{
      filters: [
        %{
          key: "campaign_email_audience_list",
          value:
            Enum.join(
              [
                unsubscriber_scope
                | Enum.map(recipient_list_type, &String.upcase(Atom.to_string(&1)))
              ],
              ","
            )
        },
        %{key: "has_email", value: "true"}
      ]
    })
    |> subquery()
    |> distinct([c], c.id)
    |> Gaia.Repo.all()
  end

  @doc """
  This script is to add `media_id` into existing announcements.
  i.e. you change media_announcements to a different company,
  you need to generate the media for each one.
  """
  def migration_script(market_key \\ "ASX") do
    tickers = Gaia.Markets.get_all_existing_client_tickers(market_key)

    listing_keys =
      Enum.map(tickers, fn %Gaia.Markets.Ticker{listing_key: listing_key} ->
        String.upcase(listing_key)
      end)

    MediaAnnouncement
    |> where(
      [ma],
      ma.listing_key in ^listing_keys and ma.market_key == ^String.downcase(market_key)
    )
    |> Repo.all()
    |> migration_script_exec(tickers)
  end

  defp migration_script_exec([], _tickers), do: :ok

  defp migration_script_exec(
         [
           %MediaAnnouncement{listing_key: media_announcement_listing_key, media_id: media_id} = media_announcement
           | media_announcements
         ],
         tickers
       )
       when is_nil(media_id) do
    %Gaia.Markets.Ticker{company_profile_id: company_profile_id} =
      Enum.find(tickers, fn %Gaia.Markets.Ticker{listing_key: listing_key} ->
        media_announcement_listing_key == String.upcase(listing_key)
      end)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :media,
      change_media(%Media{}, %{company_profile_id: company_profile_id})
    )
    |> Ecto.Multi.update(:media_announcement, fn %{media: media} ->
      change_media_announcement(media_announcement, %{media_id: media.id})
    end)
    |> Repo.transaction()

    migration_script_exec(media_announcements, tickers)
  end

  defp migration_script_exec([_media_announcement | media_announcements], tickers),
    do: migration_script_exec(media_announcements, tickers)

  def media_announcements_query(args) do
    MediaQueries.media_announcements_query(args)
  end

  def get_company_latest_two_announcements(company_profile_id) do
    MediaQueries.get_company_latest_two_announcements(company_profile_id)
  end

  def get_company_latest_two_market_sensitive_announcements(company_profile_id) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "is_market_sensitive", value: "true"}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(2)
    |> Repo.all()
  end

  def get_company_latest_announcement_by_subtype(company_profile_id, subtype) do
    %{
      filters: [
        %{key: "company_profile_id", value: company_profile_id},
        %{key: "subtype", value: subtype}
      ],
      orders: [%{key: "posted_at", value: "desc"}, %{key: "id", value: "desc"}]
    }
    |> media_announcements_query()
    |> limit(1)
    |> Repo.one()
  end

  def get_company_latest_announcement_by_rectypes(company_profile_id, rectypes) do
    MediaQueries.get_company_latest_announcement_by_rectypes(company_profile_id, rectypes)
  end

  def get_oldest_media_announcement(ticker) do
    MediaQueries.get_oldest_media_announcement(ticker)
  end

  def get_oldest_media_announcement_date(ticker) do
    MediaQueries.get_oldest_media_announcement_date(ticker)
  end

  def get_media_announcements_date_range(ticker) do
    MediaAnnouncement
    |> where([ma], ma.listing_key == ^String.upcase(ticker))
    |> select([ma], %{
      oldest: min(ma.posted_at),
      newest: max(ma.posted_at)
    })
    |> Repo.one()
  end

  def batch_get_media_announcements(_, media_ids) do
    MediaAnnouncement
    |> where([ma], ma.media_id in ^media_ids)
    |> Repo.all()
  end

  def reset_media_announcement(%MediaAnnouncement{id: id} = media_announcement) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :updated_media_announcement,
      change_media_announcement(media_announcement, %{
        summary: nil,
        video_url: nil
      })
    )
    |> Ecto.Multi.delete_all(
      :delete_all_media_comments,
      from(mc in MediaComment,
        where: mc.media_id == ^media_announcement.media_id
      )
    )
    |> Ecto.Multi.delete_all(
      :delete_all_media_likes,
      from(ml in MediaLike, where: ml.media_id == ^media_announcement.media_id)
    )
    |> Ecto.Multi.delete_all(
      :delete_all_media_survey_answers,
      from(msa in MediaSurveyAnswer,
        where: msa.media_id == ^media_announcement.media_id
      )
    )
    |> Ecto.Multi.delete_all(:delete_all_trackings, fn _ ->
      from(t in Gaia.Tracking.InvestorHub,
        where: t.event == ^"#{id}_announcement_page_viewed"
      )
    end)
    |> Repo.transaction()
  end

  def get_all_existing_announcements_without_media_id(market_key, listing_key) do
    MediaAnnouncement
    |> where(
      [ma],
      ma.market_key == ^market_key and ma.listing_key == ^listing_key and
        is_nil(ma.media_id)
    )
    |> Repo.all()
  end

  @doc """
  Returns the list of interactions_media_comments.

  ## Examples

      iex> list_interactions_media_comments()
      [%MediaComment{}, ...]

  """
  def list_interactions_media_comments do
    Repo.all(MediaComment)
  end

  @doc """
  Gets a single media_comment.

  Raises `Ecto.NoResultsError` if the Media comment does not exist.

  ## Examples

      iex> get_media_comment!(123)
      %MediaComment{}

      iex> get_media_comment!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_comment!(id), do: Repo.get!(MediaComment, id)

  @doc """
  Gets a single media_comment.

  Returns `nil` if the Media comment does not exist.

  ## Examples

      iex> get_media_comment(123)
      %MediaComment{}

      iex> get_media_comment(456)
      nil

  """
  def get_media_comment(id), do: Repo.get(MediaComment, id)

  def get_media_comment_by(attrs), do: Repo.get_by(MediaComment, attrs)

  @doc """
  Creates a media_comment.

  ## Examples

      iex> create_media_comment(%{field: value})
      {:ok, %MediaComment{}}

      iex> create_media_comment(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_comment(attrs \\ %{}) do
    %MediaComment{}
    |> MediaComment.changeset(attrs)
    |> Repo.insert()
  end

  def create_media_comment_with_reply(
        %{media_id: _, private: _, content: _, comment_source: _} = question,
        %{company_author_id: _, media_id: _, content: _, private: _} = reply
      ) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(:question, %MediaComment{
      media_id: question |> Map.get(:media_id) |> String.to_integer(),
      private: Map.get(question, :private),
      content: Map.get(question, :content),
      comment_source: Map.get(question, :comment_source),
      comment_source_url: Map.get(question, :comment_source_url),
      inserted_at: Helper.ExDay.ecto_timestamp(),
      updated_at: Helper.ExDay.ecto_timestamp(),
      done: true,
      last_edited_by: Map.get(reply, :company_author_id)
    })
    |> Ecto.Multi.insert(:reply, fn %{question: %{id: parent_id}} ->
      %MediaComment{
        media_id: reply |> Map.get(:media_id) |> String.to_integer(),
        company_author_id: Map.get(reply, :company_author_id),
        private: Map.get(reply, :private),
        content: Map.get(reply, :content),
        parent_id: parent_id,
        inserted_at: Helper.ExDay.ecto_timestamp(),
        updated_at: Helper.ExDay.ecto_timestamp(),
        done: true
      }
    end)
    |> Repo.transaction()
  end

  @doc """
  Updates a media_comment.

  ## Examples

      iex> update_media_comment(media_comment, %{field: new_value})
      {:ok, %MediaComment{}}

      iex> update_media_comment(media_comment, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_comment(%MediaComment{} = media_comment, attrs) do
    media_comment
    |> MediaComment.changeset(attrs)
    |> Repo.update()
  end

  def invalidate_media_comment_and_maybe_children(%MediaComment{id: media_comment_id} = media_comment) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:media_comment, fn _ ->
      change_media_comment(media_comment, %{invalidated: true})
    end)
    |> Ecto.Multi.update_all(
      :media_comments,
      where(MediaComment, [mc], mc.parent_id == ^media_comment_id),
      set: [invalidated: true]
    )
    |> Repo.transaction()
  end

  @doc """
  Deletes a media_comment.

  ## Examples

      iex> delete_media_comment(media_comment)
      {:ok, %MediaComment{}}

      iex> delete_media_comment(media_comment)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_comment(%MediaComment{} = media_comment) do
    Repo.delete(media_comment)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_comment changes.

  ## Examples

      iex> change_media_comment(media_comment)
      %Ecto.Changeset{data: %MediaComment{}}

  """
  def change_media_comment(%MediaComment{} = media_comment, attrs \\ %{}) do
    MediaComment.changeset(media_comment, attrs)
  end

  def batch_get_total_parent_company_comment_count_for_media(_, media_ids) do
    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.parent_id) and is_nil(mc.investor_user_id) and
        not is_nil(mc.company_author_id)
    )
    |> group_by([mc], [mc.media_id])
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  # Used in hermes, should filter out private questions if that option is enabled
  def batch_get_total_parent_comment_count_for_media(_, args) do
    media_ids = get_media_ids_from_args(args)

    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.parent_id) and
        is_nil(mc.company_author_id)
    )
    |> maybe_filter_media_comment_counts_by_private_and_user_id(args)
    |> group_by([mc], [mc.media_id])
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  # Used in hermes, should filter out private questions if that option is enabled
  def batch_get_question_count_of_media(_, args) do
    media_ids = get_media_ids_from_args(args)

    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.parent_id) and
        is_nil(mc.company_author_id)
    )
    |> maybe_filter_media_comment_counts_by_private_and_user_id(args)
    |> group_by([mc], [mc.media_id])
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  # Used in hermes, automatically filters out private comments
  def batch_get_public_comment_count_of_media(_, media_ids) do
    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and not mc.private
    )
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  # Used in hermes, should filter out private questions if that option is enabled
  def batch_get_answered_question_count_of_media(_, args) do
    media_ids = get_media_ids_from_args(args)

    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and not is_nil(mc.parent_id)
    )
    |> maybe_filter_media_comment_counts_by_private_and_user_id(args)
    |> group_by([mc], [mc.media_id])
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  def batch_get_total_active_comment_count_of_media(_, media_ids) do
    answered_parent_comment_ids_query =
      MediaComment
      |> where([mc], mc.media_id in ^media_ids and not is_nil(mc.parent_id))
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.parent_id) and
        is_nil(mc.investor_user_id) and
        mc.id not in subquery(answered_parent_comment_ids_query)
    )
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  def batch_get_total_company_comment_count_of_media(_, media_ids) do
    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.investor_user_id) and
        not is_nil(mc.company_author_id) and is_nil(mc.parent_id)
    )
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  def batch_get_total_comment_count_of_media(_, media_ids) do
    MediaComment
    |> where([mc], mc.media_id in ^media_ids and is_nil(mc.investor_user_id))
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  def batch_get_total_active_question_count_of_media(_, media_ids),
    do: get_total_active_question_count_of_media(media_ids)

  def get_total_active_question_count_of_media(media_ids) do
    answered_parent_comment_ids_query =
      MediaComment
      |> where([mc], mc.media_id in ^media_ids and not is_nil(mc.parent_id))
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    MediaComment
    |> where(
      [mc],
      mc.media_id in ^media_ids and is_nil(mc.parent_id) and
        is_nil(mc.company_author_id) and
        mc.id not in subquery(answered_parent_comment_ids_query) and not mc.done
    )
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  def batch_get_total_question_count_of_media(_, media_ids) do
    MediaComment
    |> where([mc], mc.media_id in ^media_ids and is_nil(mc.company_author_id))
    |> group_by([mc], mc.media_id)
    |> select([mc], %{count: count(mc.id), media_id: mc.media_id})
    |> Repo.all()
  end

  defp get_media_ids_from_args(args) when is_list(args) do
    Enum.reduce(args, [], fn arg, acc ->
      case Map.get(arg, :media_id) do
        nil -> acc
        media_id -> [media_id | acc]
      end
    end)
  end

  defp get_media_ids_from_args(_), do: []

  defp get_hide_private_questions_in_counts_from_args(args) when is_list(args),
    do: args |> List.first() |> Map.get(:hide_private_questions_from_count, false)

  defp get_hide_private_questions_in_counts_from_args(_), do: false

  defp get_user_id_from_args(args) when is_list(args), do: args |> List.first() |> Map.get(:current_user_id)

  defp get_user_id_from_args(_), do: nil

  def get_athena_media_comments(%{media_id: media_id} = args) do
    children_query =
      MediaComment
      |> where([mc], not mc.invalidated)
      |> order_by([mc], desc: mc.inserted_at)

    MediaComment
    |> where(
      [mc],
      mc.media_id == ^media_id and is_nil(mc.parent_id)
    )
    |> maybe_filter_media_comments_by_annotation_metadata(args)
    |> order_by([mc], desc: mc.inserted_at)
    |> preload(children: ^children_query)
    |> Repo.all()
  end

  def get_company_authored_media_comments(media_id) do
    children_query =
      MediaComment
      |> where([mc], not mc.invalidated)
      |> order_by([mc], desc: mc.inserted_at)

    MediaComment
    |> where(
      [mc],
      mc.media_id == ^media_id and is_nil(mc.investor_user_id) and
        not is_nil(mc.company_author_id) and is_nil(mc.parent_id)
    )
    |> order_by([mc], desc: mc.inserted_at)
    |> preload(children: ^children_query)
    |> Repo.all()
  end

  def get_hermes_media_comments(%{media_id: media_id} = args) do
    children_query =
      MediaComment
      |> where([mc], not mc.invalidated)
      |> order_by([mc], desc: mc.inserted_at)

    user_id = Map.get(args, :user_id)

    MediaComment
    |> where([mc], mc.media_id == ^media_id and is_nil(mc.parent_id))
    |> filter_comments_by_privacy_and_current_user(Map.put(args, :user_id, user_id))
    |> maybe_filter_media_comments_by_annotation_metadata(args)
    |> order_by([mc], desc: mc.inserted_at)
    |> preload(children: ^children_query)
    |> Repo.all()
    |> maybe_hide_private_contents(args)
  end

  def get_hermes_user_comments(%{investor_user_id: investor_user_id} = args) do
    children_query =
      MediaComment
      |> where([mc], not mc.invalidated)
      |> order_by([mc], desc: mc.inserted_at)

    MediaComment
    |> where([mc], mc.investor_user_id == ^investor_user_id)
    |> order_by([mc], desc: mc.inserted_at)
    |> preload(children: ^children_query)
    |> Repo.all()
    |> maybe_hide_private_contents(args)
  end

  def get_hermes_user_media_likes(%{investor_user_id: investor_user_id}) do
    media_comments_likes_query =
      MediaCommentLike
      |> where([mc], mc.investor_user_id == ^investor_user_id and mc.like)
      |> order_by([mc], desc: mc.inserted_at)
      |> preload(
        comment: [
          :investor_user,
          :company_author,
          children: [:investor_user, :company_author]
        ]
      )
      |> Repo.all()

    media_likes_query =
      MediaLike
      |> where([mc], mc.investor_user_id == ^investor_user_id and mc.like)
      |> order_by([mc], desc: mc.inserted_at)
      |> preload(:media)
      |> Repo.all()

    (media_likes_query ++ media_comments_likes_query)
    |> Enum.sort(&(&1.inserted_at < &2.inserted_at))
    |> Enum.reduce([], fn
      %MediaCommentLike{} = comment_like, acc ->
        [
          %{
            id: "#{comment_like.id}-comment-like",
            comment_like: comment_like,
            media_like: nil
          }
          | acc
        ]

      %MediaLike{} = media_like, acc ->
        [
          %{
            id: "#{media_like.id}-media-like",
            comment_like: nil,
            media_like: media_like
          }
          | acc
        ]
    end)
  end

  def completed_media_survey_count(media_id, question) do
    MediaSurveyAnswer
    |> where([msa], msa.media_id == ^media_id and msa.question == ^question)
    |> select([msa], count(msa.investor_user_id, :distinct))
    |> Repo.one()
  end

  def get_hermes_user_surveys(%{investor_user_id: investor_user_id}) do
    MediaSurveyAnswer
    |> where([msa], msa.investor_user_id == ^investor_user_id)
    |> Repo.all()
    |> Enum.reduce(%{}, fn
      %MediaSurveyAnswer{} = msa, acc ->
        Map.update(
          acc,
          msa.media_id,
          [
            %{
              question: msa.question,
              answer: msa.answer,
              total_respondents: completed_media_survey_count(msa.media_id, msa.question)
            }
          ],
          fn value ->
            [
              %{
                question: msa.question,
                answer: msa.answer,
                total_respondents: completed_media_survey_count(msa.media_id, msa.question)
              }
            ] ++ value
          end
        )
    end)
    |> Map.to_list()
    |> Enum.map(fn {key, values} ->
      %{
        media_id: key,
        user_responses: values
      }
    end)
  end

  defp maybe_filter_media_comment_counts_by_private_and_user_id(query, args) do
    hide_private_questions_from_count = get_hide_private_questions_in_counts_from_args(args)
    user_id = get_user_id_from_args(args)

    filter_comments_by_privacy_and_current_user(query, %{
      hide_private_questions_from_count: hide_private_questions_from_count,
      user_id: user_id
    })
  end

  defp filter_comments_by_privacy_and_current_user(query, %{
         hide_private_questions_from_count: hide_private_questions_from_count,
         user_id: user_id
       }) do
    case {user_id, hide_private_questions_from_count} do
      {nil, true} ->
        # No logged in user + hide private questions setting - show all public
        where(query, [mc], not mc.private)

      {nil, false} ->
        # No logged in user + show private questions setting - show all
        query

      {_, true} ->
        # Logged in user + hide private questions setting - show all public or user's questions
        where(query, [mc], not mc.private or mc.investor_user_id == ^user_id)

      {_, false} ->
        # Logged in user + show private questions setting - show all
        query
    end
  end

  defp maybe_filter_media_comments_by_annotation_metadata(query, %{is_annotation: true}) do
    where(query, [mc], not is_nil(mc.annotation_metadata))
  end

  defp maybe_filter_media_comments_by_annotation_metadata(query, _args), do: query

  defp maybe_hide_private_contents([%MediaComment{} | _] = comments, %{user_id: user_id}) do
    Enum.map(comments, fn
      %MediaComment{
        private: true,
        children: children,
        investor_user_id: investor_user_id,
        company_author_id: company_author_id
      } = comment ->
        hide_private_content(comment, children, investor_user_id, user_id, company_author_id)

      %MediaComment{} = comment ->
        comment
    end)
  end

  defp maybe_hide_private_contents([%MediaComment{} | _] = comments, _) do
    Enum.map(comments, fn
      %MediaComment{private: true, children: children, company_author_id: company_author_id} =
          comment ->
        child_match_and_replace(comment, children, company_author_id)

      %MediaComment{} = comment ->
        comment
    end)
  end

  defp maybe_hide_private_contents([], _), do: []
  defp maybe_hide_private_contents(_, _), do: []

  defp hide_private_content(comment, children, investor_user_id, user_id, company_author_id) do
    if match?([_ | _], children) and investor_user_id != user_id do
      replace_children(comment)
    else
      replace_if_same_user(comment, investor_user_id, user_id, company_author_id)
    end
  end

  defp replace_children(comment) do
    comment
    |> Map.replace!(:content, "")
    |> Map.update!(:children, fn children ->
      Enum.map(children, &replace_content(&1))
    end)
  end

  defp replace_content(%MediaComment{} = comment), do: Map.replace!(comment, :content, "")

  defp replace_if_same_user(comment, investor_user_id, user_id, company_author_id) do
    if investor_user_id == user_id or not is_nil(company_author_id) do
      comment
    else
      replace_content(comment)
    end
  end

  defp child_match_and_replace(comment, children, company_author_id) do
    if match?([_ | _], children) do
      replace_children(comment)
    else
      if is_nil(company_author_id) do
        replace_content(comment)
      else
        comment
      end
    end
  end

  def get_media_comment_with_media(id) do
    MediaComment
    |> join(:left, [mc], m in assoc(mc, :media))
    |> preload([_, m], media: m)
    |> Repo.get(id)
  end

  def reply_to_media_comment(parent_id, %{private: private} = args) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(:insert, change_media_comment(%MediaComment{}, args))
    |> Ecto.Multi.update_all(
      :update_all,
      MediaComment.comment_thread_query(parent_id),
      set: [private: private]
    )
    |> Repo.transaction()
  end

  def toggle_media_comment_privacy(id, private) do
    id
    |> MediaComment.comment_thread_query()
    |> Repo.update_all(set: [private: private])
  end

  def count_total_media_comments_for_company(company_profile_id) do
    MediaComment
    |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where([mc], is_nil(mc.company_author_id))
    |> Repo.aggregate(:count)
  end

  def count_total_media_comments_for_company(company_profile_id, start_date, end_date) do
    MediaComment
    |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where(
      [mc],
      mc.inserted_at >= ^start_date and mc.inserted_at <= ^end_date
    )
    |> where([mc], is_nil(mc.company_author_id))
    |> Repo.aggregate(:count)
  end

  def count_total_active_media_comments_for_company(company_profile_id) do
    answered_parent_comment_ids_query =
      MediaComment
      |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
      |> where([mc], not is_nil(mc.parent_id))
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    MediaComment
    |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where(
      [mc],
      is_nil(mc.parent_id) and
        mc.id not in subquery(answered_parent_comment_ids_query) and not mc.done
    )
    |> where([mc], is_nil(mc.company_author_id))
    |> Repo.aggregate(:count)
  end

  def media_questions_query(args, company_profile_id, company_user_id) do
    children_query =
      MediaComment
      |> where([mc], not mc.invalidated)
      |> order_by([mc], desc: mc.inserted_at)

    Enum.reduce(args, MediaComment, fn
      {:filters, filters}, query ->
        query
        |> where([mc], is_nil(mc.parent_id))
        |> media_questions_filter_with(
          filters,
          company_profile_id,
          company_user_id
        )
        |> media_questions_filter_starred(filters, company_user_id)
        |> media_questions_filter_tabs(
          filters,
          company_profile_id,
          company_user_id
        )
        |> media_questions_filter_comment_value(filters)
        |> preload(children: ^children_query)
        |> preload([:investor_user, :media])

      {:orders, orders}, query ->
        media_comments_order_with(query, orders)

      _, query ->
        query
    end)
  end

  defp media_questions_filter_starred(query, filter, company_user_id) do
    starred_comment_query =
      MediaCommentStar
      |> where([mcs], mcs.starred and mcs.company_user_id == ^company_user_id)
      |> select([mcs], mcs.media_comment_id)

    Enum.reduce(filter, query, fn
      %{key: "starred", value: "true"}, query ->
        from(q in query,
          where: is_nil(q.parent_id) and q.id in subquery(starred_comment_query)
        )

      %{key: "tab-starred", value: "true"}, query ->
        from(q in query,
          where: is_nil(q.parent_id) and q.id in subquery(starred_comment_query)
        )

      _, query ->
        query
    end)
  end

  defp media_questions_filter_with(query, filter, company_profile_id, company_user_id) do
    answered_parent_comment_ids_query =
      MediaComment
      |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
      |> where([mc], not is_nil(mc.parent_id) and not mc.invalidated)
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    read_subquery =
      MediaCommentRead
      |> where([mcr], mcr.read and mcr.company_user_id == ^company_user_id)
      |> select([mcr], mcr.media_comment_id)

    Enum.reduce(filter, query, fn
      %{key: "active", value: "true"}, query ->
        from(q in query,
          where:
            is_nil(q.parent_id) and
              q.id not in subquery(answered_parent_comment_ids_query) and
              not q.done
        )

      %{key: "done", value: "true"}, query ->
        from(q in query,
          where:
            is_nil(q.parent_id) and
              (q.id in subquery(answered_parent_comment_ids_query) or q.done)
        )

      %{key: "new", value: "true"}, query ->
        from(q in query,
          where: q.id not in subquery(read_subquery) and is_nil(q.parent_id)
        )

      _, query ->
        query
    end)
  end

  defp media_questions_filter_tabs(query, filter, company_profile_id, company_user_id) do
    answered_parent_comment_ids_query =
      MediaComment
      |> join(:inner, [mc], m in Media, on: mc.media_id == m.id and m.company_profile_id == ^company_profile_id)
      |> where([mc], not is_nil(mc.parent_id) and not mc.invalidated)
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    read_subquery =
      MediaCommentRead
      |> where([mcr], mcr.read and mcr.company_user_id == ^company_user_id)
      |> select([mcr], mcr.media_comment_id)

    Enum.reduce(filter, query, fn
      %{key: "tab-active", value: "true"}, query ->
        from(q in query,
          where:
            is_nil(q.parent_id) and
              q.id not in subquery(answered_parent_comment_ids_query) and
              not q.done
        )

      %{key: "tab-done", value: "true"}, query ->
        from(q in query,
          where:
            is_nil(q.parent_id) and
              (q.id in subquery(answered_parent_comment_ids_query) or q.done)
        )

      %{key: "tab-new", value: "true"}, query ->
        from(q in query,
          where: q.id not in subquery(read_subquery) and is_nil(q.parent_id)
        )

      _, query ->
        query
    end)
  end

  defp media_questions_filter_comment_value(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        from(q in query,
          join: m in assoc(q, :media),
          where: m.company_profile_id == ^value
        )

      %{key: "media_id", value: media_id}, query ->
        from(q in query, where: q.media_id == ^media_id)

      %{key: "shareholders", value: "true"}, query ->
        from(q in query,
          join: iu in assoc(q, :investor_user),
          join: c in assoc(iu, :contact),
          as: :contact,
          where:
            is_nil(q.parent_id) and
              exists(
                Gaia.Registers.Shareholding
                |> where([sh], sh.contact_id == parent_as(:contact).id)
                |> select(1)
              )
        )

      %{key: "private", value: "true"}, query ->
        from(q in query, where: q.private and is_nil(q.parent_id))

      %{key: "private", value: "false"}, query ->
        from(q in query, where: not q.private and is_nil(q.parent_id))

      %{key: "posted_at_greater_than", value: value}, query ->
        from(q in query, where: q.inserted_at > ^value)

      %{key: "posted_at_less_than", value: value}, query ->
        from(q in query, where: q.inserted_at < ^value)

      %{key: "search", value: value}, query ->
        from(q in query, where: ilike(q.content, ^"%#{value}%"))

      _, query ->
        query
    end)
  end

  defp media_comments_order_with(query, order) do
    Enum.reduce(order, query, fn
      %{key: "posted_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :inserted_at])

      %{key: "posted_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :inserted_at])

      %{key: "most_upvoted", value: "desc"}, query ->
        from(q in query, order_by: [desc: :likes])

      %{key: "type", value: "popular"}, query ->
        media_comments_popular_sort_query(query)

      %{key: "type", value: "recent"}, query ->
        from(q in query, order_by: [desc: :inserted_at])

      _, query ->
        query
    end)
  end

  def get_media_comment_star_for_profile_user(media_comment_id, company_user_id) do
    MediaCommentStar
    |> where(
      [mcs],
      mcs.media_comment_id == ^media_comment_id and
        mcs.company_user_id == ^company_user_id
    )
    |> Repo.one()
  end

  def get_media_comment_read_for_profile_user(media_comment_id, company_user_id) do
    MediaCommentRead
    |> where(
      [mcr],
      mcr.media_comment_id == ^media_comment_id and
        mcr.company_user_id == ^company_user_id
    )
    |> Repo.one()
  end

  @doc """
  Returns the list of interactions_media_comment_likes.

  ## Examples

      iex> list_interactions_media_comment_likes()
      [%MediaCommentLike{}, ...]

  """
  def list_interactions_media_comment_likes do
    Repo.all(MediaCommentLike)
  end

  @doc """
  Gets a single media_comment_like.

  Raises `Ecto.NoResultsError` if the Media comment like does not exist.

  ## Examples

      iex> get_media_comment_like!(123)
      %MediaCommentLike{}

      iex> get_media_comment_like!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_comment_like!(id), do: Repo.get!(MediaCommentLike, id)

  @doc """
  Creates a media_comment_like.

  ## Examples

      iex> create_media_comment_like(%{field: value})
      {:ok, %MediaCommentLike{}}

      iex> create_media_comment_like(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_comment_like(attrs \\ %{}) do
    %MediaCommentLike{}
    |> MediaCommentLike.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_comment_like.

  ## Examples

      iex> update_media_comment_like(media_comment_like, %{field: new_value})
      {:ok, %MediaCommentLike{}}

      iex> update_media_comment_like(media_comment_like, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_comment_like(%MediaCommentLike{} = media_comment_like, attrs) do
    media_comment_like
    |> MediaCommentLike.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_comment_like.

  ## Examples

      iex> delete_media_comment_like(media_comment_like)
      {:ok, %MediaCommentLike{}}

      iex> delete_media_comment_like(media_comment_like)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_comment_like(%MediaCommentLike{} = media_comment_like) do
    Repo.delete(media_comment_like)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_comment_like changes.

  ## Examples

      iex> change_media_comment_like(media_comment_like)
      %Ecto.Changeset{data: %MediaCommentLike{}}

  """
  def change_media_comment_like(%MediaCommentLike{} = media_comment_like, attrs \\ %{}) do
    MediaCommentLike.changeset(media_comment_like, attrs)
  end

  def batch_get_media_comment_likes(_, media_comment_ids) do
    MediaCommentLike
    |> where(
      [mcl],
      mcl.announcement_comment_id in ^media_comment_ids and mcl.like
    )
    |> select([mcl], %{
      investor_user_id: mcl.investor_user_id,
      media_comment_id: mcl.announcement_comment_id
    })
    |> Repo.all()
  end

  def upsert_media_comment_like(%{investor_user_id: investor_user_id, like: like, media_comment_id: media_comment_id}) do
    case Repo.get_by(MediaCommentLike, %{
           announcement_comment_id: media_comment_id,
           investor_user_id: investor_user_id
         }) do
      %MediaCommentLike{} = media_comment_like ->
        update_media_comment_like(media_comment_like, %{like: like})

      _ ->
        create_media_comment_like(%{
          announcement_comment_id: media_comment_id,
          investor_user_id: investor_user_id,
          like: like
        })
    end
  end

  @doc """
  Returns the list of interactions_media_comment_reads.

  ## Examples

      iex> list_interactions_media_comment_reads()
      [%MediaCommentRead{}, ...]

  """
  def list_interactions_media_comment_reads do
    Repo.all(MediaCommentRead)
  end

  @doc """
  Gets a single media_comment_read.

  Raises `Ecto.NoResultsError` if the Media comment read does not exist.

  ## Examples

      iex> get_media_comment_read!(123)
      %MediaCommentRead{}

      iex> get_media_comment_read!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_comment_read!(id), do: Repo.get!(MediaCommentRead, id)

  @doc """
  Creates a media_comment_read.

  ## Examples

      iex> create_media_comment_read(%{field: value})
      {:ok, %MediaCommentRead{}}

      iex> create_media_comment_read(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_comment_read(attrs \\ %{}) do
    %MediaCommentRead{}
    |> MediaCommentRead.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_comment_read.

  ## Examples

      iex> update_media_comment_read(media_comment_read, %{field: new_value})
      {:ok, %MediaCommentRead{}}

      iex> update_media_comment_read(media_comment_read, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_comment_read(%MediaCommentRead{} = media_comment_read, attrs) do
    media_comment_read
    |> MediaCommentRead.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_comment_read.

  ## Examples

      iex> delete_media_comment_read(media_comment_read)
      {:ok, %MediaCommentRead{}}

      iex> delete_media_comment_read(media_comment_read)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_comment_read(%MediaCommentRead{} = media_comment_read) do
    Repo.delete(media_comment_read)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_comment_read changes.

  ## Examples

      iex> change_media_comment_read(media_comment_read)
      %Ecto.Changeset{data: %MediaCommentRead{}}

  """
  def change_media_comment_read(%MediaCommentRead{} = media_comment_read, attrs \\ %{}) do
    MediaCommentRead.changeset(media_comment_read, attrs)
  end

  def upsert_media_comment_read(%{company_user_id: company_user_id, media_comment_id: media_comment_id} = args) do
    case Repo.get_by(MediaCommentRead, %{
           company_user_id: company_user_id,
           media_comment_id: media_comment_id
         }) do
      %MediaCommentRead{} = mcr ->
        update_media_comment_read(mcr, args)

      _ ->
        create_media_comment_read(args)
    end
  end

  @doc """
  Returns the list of interactions_media_comment_stars.

  ## Examples

      iex> list_interactions_media_comment_stars()
      [%MediaCommentStar{}, ...]

  """
  def list_interactions_media_comment_stars do
    Repo.all(MediaCommentStar)
  end

  @doc """
  Gets a single media_comment_star.

  Raises `Ecto.NoResultsError` if the Media comment star does not exist.

  ## Examples

      iex> get_media_comment_star!(123)
      %MediaCommentStar{}

      iex> get_media_comment_star!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_comment_star!(id), do: Repo.get!(MediaCommentStar, id)

  @doc """
  Creates a media_comment_star.

  ## Examples

      iex> create_media_comment_star(%{field: value})
      {:ok, %MediaCommentStar{}}

      iex> create_media_comment_star(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_comment_star(attrs \\ %{}) do
    %MediaCommentStar{}
    |> MediaCommentStar.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_comment_star.

  ## Examples

      iex> update_media_comment_star(media_comment_star, %{field: new_value})
      {:ok, %MediaCommentStar{}}

      iex> update_media_comment_star(media_comment_star, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_comment_star(%MediaCommentStar{} = media_comment_star, attrs) do
    media_comment_star
    |> MediaCommentStar.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_comment_star.

  ## Examples

      iex> delete_media_comment_star(media_comment_star)
      {:ok, %MediaCommentStar{}}

      iex> delete_media_comment_star(media_comment_star)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_comment_star(%MediaCommentStar{} = media_comment_star) do
    Repo.delete(media_comment_star)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_comment_star changes.

  ## Examples

      iex> change_media_comment_star(media_comment_star)
      %Ecto.Changeset{data: %MediaCommentStar{}}

  """
  def change_media_comment_star(%MediaCommentStar{} = media_comment_star, attrs \\ %{}) do
    MediaCommentStar.changeset(media_comment_star, attrs)
  end

  def upsert_media_comment_star(%{company_user_id: company_user_id, media_comment_id: media_comment_id} = args) do
    case Repo.get_by(MediaCommentStar, %{
           company_user_id: company_user_id,
           media_comment_id: media_comment_id
         }) do
      %MediaCommentStar{} = mcr ->
        update_media_comment_star(mcr, args)

      _ ->
        create_media_comment_star(args)
    end
  end

  @doc """
  Returns the list of interactions_media_likes.

  ## Examples

      iex> list_interactions_media_likes()
      [%MediaLike{}, ...]

  """
  def list_interactions_media_likes do
    Repo.all(MediaLike)
  end

  @doc """
  Gets a single media_like.

  Raises `Ecto.NoResultsError` if the Media like does not exist.

  ## Examples

      iex> get_media_like!(123)
      %MediaLike{}

      iex> get_media_like!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_like!(id), do: Repo.get!(MediaLike, id)

  def get_media_like_by(opts), do: Repo.get_by(MediaLike, opts)

  @doc """
  Creates a media_like.

  ## Examples

      iex> create_media_like(%{field: value})
      {:ok, %MediaLike{}}

      iex> create_media_like(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_like(attrs \\ %{}) do
    %MediaLike{}
    |> MediaLike.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_like.

  ## Examples

      iex> update_media_like(media_like, %{field: new_value})
      {:ok, %MediaLike{}}

      iex> update_media_like(media_like, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_like(%MediaLike{} = media_like, attrs) do
    media_like
    |> MediaLike.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_like.

  ## Examples

      iex> delete_media_like(media_like)
      {:ok, %MediaLike{}}

      iex> delete_media_like(media_like)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_like(%MediaLike{} = media_like) do
    Repo.delete(media_like)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_like changes.

  ## Examples

      iex> change_media_like(media_like)
      %Ecto.Changeset{data: %MediaLike{}}

  """
  def change_media_like(%MediaLike{} = media_like, attrs \\ %{}) do
    MediaLike.changeset(media_like, attrs)
  end

  def total_media_likes(media_id),
    do: MediaLike |> where([ml], ml.media_id == ^media_id and ml.like) |> Repo.aggregate(:count)

  def batch_get_media_likes(_, queries) do
    media_ids = Enum.map(queries, & &1.media_id)
    investor_user_ids = Enum.map(queries, & &1.investor_user_id)

    MediaLike
    |> where(
      [ml],
      ml.media_id in ^media_ids and
        ml.investor_user_id in ^investor_user_ids
    )
    |> Repo.all()
  end

  def batch_get_total_media_likes(_, media_ids) do
    MediaLike
    |> where(
      [ml],
      ml.media_id in ^media_ids and ml.like == true
    )
    |> group_by([ml], ml.media_id)
    |> select([ml], %{
      count: count(ml.id),
      media_id: ml.media_id
    })
    |> Repo.all()
  end

  def upsert_media_like(%{investor_user_id: investor_user_id, like: like, media_id: media_id}) do
    case Repo.get_by(MediaLike, %{
           investor_user_id: investor_user_id,
           media_id: media_id
         }) do
      %MediaLike{} = media_like ->
        update_media_like(media_like, %{like: like})

      _ ->
        create_media_like(%{
          investor_user_id: investor_user_id,
          like: like,
          media_id: media_id
        })
    end
  end

  def count_total_media_likes_for_company(company_profile_id) do
    MediaLike
    |> join(:inner, [ml], m in Media,
      on:
        ml.media_id == m.id and m.company_profile_id == ^company_profile_id and
          ml.like
    )
    |> Repo.aggregate(:count)
  end

  def count_total_media_likes_for_company(company_profile_id, start_date, end_date) do
    MediaLike
    |> join(:inner, [ml], m in Media,
      on:
        ml.media_id == m.id and m.company_profile_id == ^company_profile_id and
          ml.like
    )
    |> where(
      [ml],
      ml.inserted_at >= ^start_date and ml.inserted_at <= ^end_date
    )
    |> Repo.aggregate(:count)
  end

  @doc """
  Returns the list of interactions_media_survey_answers.

  ## Examples

      iex> list_interactions_media_survey_answers()
      [%MediaSurveyAnswer{}, ...]

  """
  def list_interactions_media_survey_answers do
    Repo.all(MediaSurveyAnswer)
  end

  @doc """
  Gets a single media_survey_answer.

  Raises `Ecto.NoResultsError` if the Media survey answer does not exist.

  ## Examples

      iex> get_media_survey_answer!(123)
      %MediaSurveyAnswer{}

      iex> get_media_survey_answer!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_survey_answer!(id), do: Repo.get!(MediaSurveyAnswer, id)

  @doc """
  Creates a media_survey_answer.

  ## Examples

      iex> create_media_survey_answer(%{field: value})
      {:ok, %MediaSurveyAnswer{}}

      iex> create_media_survey_answer(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_survey_answer(attrs \\ %{}) do
    %MediaSurveyAnswer{}
    |> MediaSurveyAnswer.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_survey_answer.

  ## Examples

      iex> update_media_survey_answer(media_survey_answer, %{field: new_value})
      {:ok, %MediaSurveyAnswer{}}

      iex> update_media_survey_answer(media_survey_answer, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_survey_answer(%MediaSurveyAnswer{} = media_survey_answer, attrs) do
    media_survey_answer
    |> MediaSurveyAnswer.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_survey_answer.

  ## Examples

      iex> delete_media_survey_answer(media_survey_answer)
      {:ok, %MediaSurveyAnswer{}}

      iex> delete_media_survey_answer(media_survey_answer)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_survey_answer(%MediaSurveyAnswer{} = media_survey_answer) do
    Repo.delete(media_survey_answer)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_survey_answer changes.

  ## Examples

      iex> change_media_survey_answer(media_survey_answer)
      %Ecto.Changeset{data: %MediaSurveyAnswer{}}

  """
  def change_media_survey_answer(%MediaSurveyAnswer{} = media_survey_answer, attrs \\ %{}) do
    MediaSurveyAnswer.changeset(media_survey_answer, attrs)
  end

  def get_investor_media_survey_answers(investor_id, media_id) do
    MediaSurveyAnswer
    |> where(
      [msa],
      msa.media_id == ^media_id and msa.investor_user_id == ^investor_id
    )
    |> select([msa], %{question: msa.question, answer: msa.answer})
    |> Repo.all()
  end

  def get_media_survey_results(media_id) do
    MediaSurveyAnswer
    |> where([msa], msa.media_id == ^media_id)
    |> group_by([msa], [msa.question, msa.answer])
    |> select([msa], %{
      answer: msa.answer,
      count: fragment("count(?)", msa.id),
      question: msa.question
    })
    |> Repo.all()
  end

  def get_media_survey_total_responses(media_id) do
    MediaSurveyAnswer
    |> where([msa], msa.media_id == ^media_id)
    |> group_by([msa], [msa.question])
    |> select([msa], {msa.question, fragment("count(?)", msa.id)})
    |> Repo.all()
  end

  def has_investor_answered_all_questions?(investor_id, media_id) do
    questions = MediaSurveyAnswer.get_questions()

    MediaSurveyAnswer
    |> where(
      [msa],
      msa.media_id == ^media_id and msa.investor_user_id == ^investor_id and
        msa.question in ^questions
    )
    |> group_by([msa], [msa.media_id])
    |> select([msa], [count(msa.id)])
    |> limit(1)
    |> Repo.one()
    |> Kernel.==([length(questions)])
  end

  def count_total_distinct_survey_responses_for_company(company_profile_id) do
    MediaSurveyAnswer
    |> join(:inner, [msa], m in Media, on: msa.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> distinct([msa], [msa.media_id, msa.investor_user_id])
    |> Repo.aggregate(:count)
  end

  def count_total_distinct_survey_responses_for_company(company_profile_id, start_date, end_date) do
    MediaSurveyAnswer
    |> join(:inner, [msa], m in Media, on: msa.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where(
      [msa],
      msa.inserted_at >= ^start_date and msa.inserted_at <= ^end_date
    )
    |> distinct([msa], [msa.media_id, msa.investor_user_id])
    |> Repo.aggregate(:count)
  end

  def count_total_distinct_survey_responses_for_media(media_id) do
    MediaSurveyAnswer
    |> where([msa], msa.media_id == ^media_id)
    |> distinct([msa], msa.investor_user_id)
    |> Repo.aggregate(:count)
  end

  def get_media_survey_response_stats(media_id) do
    MediaSurveyAnswer
    |> where([msa], msa.media_id == ^media_id)
    |> group_by([msa], [msa.question, msa.answer])
    |> select([msa], %{
      answer: msa.answer,
      question: msa.question,
      total_responses: count(msa.investor_user_id, :distinct)
    })
    |> Repo.all()
    |> Enum.group_by(& &1.question)
    |> Enum.map(fn {question, results} ->
      sum_responses = results |> Enum.map(& &1.total_responses) |> Enum.sum()

      %{
        answers:
          Enum.map(results, fn %{
                                 answer: answer,
                                 total_responses: total_responses
                               } ->
            %{
              answer: answer,
              response_percentage_as_float: total_responses / sum_responses
            }
          end),
        question: question,
        total_responses: sum_responses
      }
    end)
  end

  def get_media_survey_answers_for_media(media_id) do
    MediaSurveyAnswer
    |> join(:left, [msa], m in assoc(msa, :media))
    |> where([msa], msa.media_id == ^media_id)
    |> preload([_, m], media: m)
    |> Repo.all()
  end

  @doc """
  Returns the list of interactions_media_updates.

  ## Examples

      iex> list_interactions_media_updates()
      [%MediaUpdate{}, ...]

  """
  def list_interactions_media_updates do
    Repo.all(MediaUpdate)
  end

  @doc """
  Gets a single media_update.

  Raises `Ecto.NoResultsError` if the Media update does not exist.

  ## Examples

      iex> get_media_update!(123)
      %MediaUpdate{}

      iex> get_media_update!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_update!(id), do: Repo.get!(MediaUpdate, id)

  def get_media_update_by(attrs), do: Repo.get_by(MediaUpdate, attrs)

  @doc """
  Gets a single media_update.

  Returns `nil` if the Media update does not exist.

  ## Examples

      iex> get_media_update(123)
      %MediaUpdate{}

      iex> get_media_update(456)
      nil

  """
  def get_media_update(id) do
    MediaUpdate
    |> join(:left, [mu], m in assoc(mu, :media))
    |> preload([_, m], media: m)
    |> Repo.get(id)
  end

  @doc """
  Creates a media_update.

  ## Examples

      iex> create_media_update(%{field: value})
      {:ok, %MediaUpdate{}}

      iex> create_media_update(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_update(attrs \\ %{}) do
    %MediaUpdate{}
    |> MediaUpdate.changeset(attrs)
    |> Repo.insert()
  end

  def create_newsflow_media_update(attrs \\ %{}) do
    %MediaUpdate{}
    |> MediaUpdate.newsflow_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_update.

  ## Examples

      iex> update_media_update(media_update, %{field: new_value})
      {:ok, %MediaUpdate{}}

      iex> update_media_update(media_update, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_update(%MediaUpdate{} = media_update, attrs) do
    media_update
    |> MediaUpdate.changeset(attrs)
    |> Repo.update()
  end

  def newsflow_update_media_update(%MediaUpdate{} = media_update, attrs) do
    media_update
    |> MediaUpdate.newsflow_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_update.

  ## Examples

      iex> delete_media_update(media_update)
      {:ok, %MediaUpdate{}}

      iex> delete_media_update(media_update)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_update(%MediaUpdate{} = media_update) do
    Repo.delete(media_update)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_update changes.

  ## Examples

      iex> change_media_update(media_update)
      %Ecto.Changeset{data: %MediaUpdate{}}

  """
  def change_media_update(%MediaUpdate{} = media_update, attrs \\ %{}) do
    MediaUpdate.changeset(media_update, attrs)
  end

  def media_updates_query(args) do
    Enum.reduce(args, MediaUpdate, fn
      {:filters, filters}, query ->
        media_updates_filter_with(query, filters)

      {:orders, orders}, query ->
        media_updates_order_with(query, orders)

      _, query ->
        query
    end)
  end

  defp media_updates_filter_with(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        from(q in query,
          join: m in assoc(q, :media),
          where: m.company_profile_id == ^value
        )

      %{key: "is_draft", value: "false"}, query ->
        from(q in query, where: not q.is_draft)

      %{key: "is_draft", value: "true"}, query ->
        from(q in query, where: q.is_draft)

      %{key: "posted_at_greater_than", value: value}, query ->
        from(q in query, where: q.posted_at > ^value)

      %{key: "posted_at_less_than", value: value}, query ->
        from(q in query, where: q.posted_at < ^value)

      %{key: "posted_by", value: value}, query ->
        from(q in query, where: q.posted_by_id == ^value)

      %{key: "search", value: value}, query ->
        from(q in query, where: ilike(q.title, ^"%#{value}%"))

      %{key: "is_distributed", value: "true"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], not is_nil(media.email_distribution_method))

      %{key: "is_distributed", value: "false"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], is_nil(media.email_distribution_method))

      %{key: "distribution_method", value: "manual"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], media.email_distribution_method == :manual)

      %{key: "distribution_method", value: "automated"}, query ->
        query
        |> join(:inner, [q], media in assoc(q, :media))
        |> where([_q, media], media.email_distribution_method == :automated)

      %{key: "tags", value: tags}, query ->
        MediaQueries.tags_filter_query(query, tags)

      %{key: "type", value: value}, query ->
        from(q in query, where: ^value in q.included_types)

      %{key: "types", value: value}, query ->
        included_types = value |> String.downcase() |> String.split(",")
        from(q in query, where: fragment("? && ?", ^included_types, q.included_types))
    end)
  end

  defp media_updates_order_with(query, order) do
    Enum.reduce(order, query, fn
      # the pinned media update should always be at the top of the list (only ever 1 max)
      %{key: "is_pinned", value: "desc"}, query ->
        from(q in query, order_by: [desc: :is_pinned])

      %{key: "posted_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :posted_at])

      %{key: "posted_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :posted_at])

      %{key: "updated_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :updated_at])

      %{key: "updated_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :updated_at])
    end)
  end

  def get_latest_media_update_for_company(company_profile_id) do
    MediaUpdate
    |> where([mu], mu.company_profile_id == ^company_profile_id)
    |> where([mu], not is_nil(mu.posted_at))
    |> where([mu], not mu.is_draft)
    |> order_by([mu], desc: mu.posted_at)
    |> limit(1)
    |> Repo.one()
  end

  def get_latest_announcement_without_video_for_company(company_profile_id) do
    MediaAnnouncement
    |> join(:inner, [ma], m in Media, on: ma.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where([ma], not is_nil(ma.posted_at))
    |> where([ma], is_nil(ma.video_url))
    |> order_by([mu], desc: mu.posted_at)
    |> limit(1)
    |> Repo.one()
  end

  def get_oldest_media_update_date_for_company(company_profile_id),
    do:
      MediaUpdate
      |> where([update], update.company_profile_id == ^company_profile_id and not is_nil(update.posted_at))
      |> order_by([update], asc: update.posted_at)
      |> select([update], update.posted_at)
      |> limit(1)
      |> Repo.one()

  def get_media_updates_date_range(company_profile_id) do
    MediaUpdate
    |> where([mu], mu.company_profile_id == ^company_profile_id)
    |> select([mu], %{
      oldest: min(mu.posted_at),
      newest: max(mu.posted_at)
    })
    |> Repo.one()
  end

  def create_new_media_update(%{company_profile_id: company_profile_id} = args) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :media,
      change_media(%Media{}, %{company_profile_id: company_profile_id})
    )
    |> Ecto.Multi.insert(:media_update, fn %{media: media} ->
      change_media_update(%MediaUpdate{}, Map.put(args, :media_id, media.id))
    end)
    |> Repo.transaction()
  end

  def update_pinned_media_update(%MediaUpdate{company_profile_id: company_profile_id} = media_update_to_be_pinned) do
    case get_company_current_pinned_media_update(company_profile_id) do
      %MediaUpdate{} = media_update_to_be_unpinned ->
        Ecto.Multi.new()
        |> Ecto.Multi.update(
          :unpin_current_pinned_media_update,
          change_media_update(media_update_to_be_unpinned, %{is_pinned: false})
        )
        |> Ecto.Multi.update(
          :pin_new_media_update,
          change_media_update(
            media_update_to_be_pinned,
            %{is_pinned: true}
          )
        )
        |> Repo.transaction()
        |> case do
          {:ok, %{pin_new_media_update: new_media_update}} ->
            {:ok, new_media_update}

          other ->
            {:error, other}
        end

      nil ->
        update_media_update(media_update_to_be_pinned, %{
          is_pinned: true
        })
    end
  end

  def get_company_current_pinned_media_update(company_profile_id) do
    MediaUpdate
    |> where([mu], mu.is_pinned == true and mu.company_profile_id == ^company_profile_id)
    |> Repo.one()
  end

  @doc """
  Returns the list of interactions_media_update_attachments.

  ## Examples

      iex> list_interactions_media_update_attachments()
      [%MediaUpdateAttachment{}, ...]

  """
  def list_interactions_media_update_attachments do
    Repo.all(MediaUpdateAttachment)
  end

  @doc """
  Gets a single media_update_attachment.

  Raises `Ecto.NoResultsError` if the Media update attachment does not exist.

  ## Examples

      iex> get_media_update_attachment!(123)
      %MediaUpdateAttachment{}

      iex> get_media_update_attachment!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_update_attachment!(id), do: Repo.get!(MediaUpdateAttachment, id)

  @doc """
  Creates a media_update_attachment.

  ## Examples

      iex> create_media_update_attachment(%{field: value})
      {:ok, %MediaUpdateAttachment{}}

      iex> create_media_update_attachment(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_update_attachment(attrs \\ %{}) do
    %MediaUpdateAttachment{}
    |> MediaUpdateAttachment.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_update_attachment.

  ## Examples

      iex> update_media_update_attachment(media_update_attachment, %{field: new_value})
      {:ok, %MediaUpdateAttachment{}}

      iex> update_media_update_attachment(media_update_attachment, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_update_attachment(%MediaUpdateAttachment{} = media_update_attachment, attrs) do
    media_update_attachment
    |> MediaUpdateAttachment.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_update_attachment.

  ## Examples

      iex> delete_media_update_attachment(media_update_attachment)
      {:ok, %MediaUpdateAttachment{}}

      iex> delete_media_update_attachment(media_update_attachment)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_update_attachment(%MediaUpdateAttachment{} = media_update_attachment) do
    Repo.delete(media_update_attachment)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_update_attachment changes.

  ## Examples

      iex> change_media_update_attachment(media_update_attachment)
      %Ecto.Changeset{data: %MediaUpdateAttachment{}}

  """
  def change_media_update_attachment(%MediaUpdateAttachment{} = media_update_attachment, attrs \\ %{}) do
    MediaUpdateAttachment.changeset(media_update_attachment, attrs)
  end

  # Get a preview thumbnail attachment by media_update_id with this hierarchy
  # 1. video + thumbnail url
  # 2. image
  # 3. pdf
  # 4. url
  # 5. ascending order_id
  def get_thumbnail_attachment_by_media_update_id(id) do
    MediaUpdateAttachment
    |> where([mua], mua.media_update_id == ^id)
    |> where(
      [mua],
      (mua.type == :video and not is_nil(mua.thumbnail_url)) or
        (mua.type != :video and not is_nil(mua.url))
    )
    |> order_by(
      [mua],
      fragment(
        "CASE WHEN ? = ? THEN 1 WHEN ? = ? THEN 2 WHEN ? = ? THEN 3 ELSE 4 END",
        mua.type,
        "video",
        mua.type,
        "image",
        mua.type,
        "pdf"
      )
    )
    |> order_by([mua], asc: mua.order_id)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Returns the list of interactions_media_update_contents.

  ## Examples

      iex> list_interactions_media_update_contents()
      [%MediaUpdateContent{}, ...]

  """
  def list_interactions_media_update_contents do
    Repo.all(MediaUpdateContent)
  end

  @doc """
  Gets a single media_update_content.

  Raises `Ecto.NoResultsError` if the Media update content does not exist.

  ## Examples

      iex> get_media_update_content!(123)
      %MediaUpdateContent{}

      iex> get_media_update_content!(456)
      ** (Ecto.NoResultsError)

  """
  def get_media_update_content!(id), do: Repo.get!(MediaUpdateContent, id)
  def get_media_update_content_by(opts), do: Repo.get_by(MediaUpdateContent, opts)

  @doc """
  Creates a media_update_content.

  ## Examples

      iex> create_media_update_content(%{field: value})
      {:ok, %MediaUpdateContent{}}

      iex> create_media_update_content(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_update_content(attrs \\ %{}) do
    %MediaUpdateContent{}
    |> MediaUpdateContent.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a media_update_content.

  ## Examples

      iex> update_media_update_content(media_update_content, %{field: new_value})
      {:ok, %MediaUpdateContent{}}

      iex> update_media_update_content(media_update_content, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_update_content(%MediaUpdateContent{} = media_update_content, attrs) do
    media_update_content
    |> MediaUpdateContent.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a media_update_content.

  ## Examples

      iex> delete_media_update_content(media_update_content)
      {:ok, %MediaUpdateContent{}}

      iex> delete_media_update_content(media_update_content)
      {:error, %Ecto.Changeset{}}

  """
  def delete_media_update_content(%MediaUpdateContent{} = media_update_content) do
    Repo.delete(media_update_content)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking media_update_content changes.

  ## Examples

      iex> change_media_update_content(media_update_content)
      %Ecto.Changeset{data: %MediaUpdateContent{}}

  """
  def change_media_update_content(%MediaUpdateContent{} = media_update_content, attrs \\ %{}) do
    MediaUpdateContent.changeset(media_update_content, attrs)
  end

  def media_belongs_to_company_hub_hash_id?(media_id, hub_hash_id) do
    with {:ok, [company_profile_id]} <- Helper.Hashid.decode_id(hub_hash_id),
         true <- is_number(company_profile_id),
         true <- media_belongs_to_company?(media_id, company_profile_id) do
      {true, company_profile_id}
    else
      _ -> {false, nil}
    end
  end

  def media_belongs_to_company?(_, nil), do: false

  def media_belongs_to_company?(media_id, company_profile_id) do
    Media
    |> where(
      [m],
      m.id == ^media_id and m.company_profile_id == ^company_profile_id
    )
    |> Repo.exists?()
  end

  def get_magic_link_id_from_announcement_html(html, text, %{
        company_profile: %{ticker: %{listing_key: _} = ticker, custom_domain: custom_domain}
      }) do
    market_listing_key = Gaia.Markets.Ticker.resolve_market_listing_key(ticker)

    href_link = find_hub_href_magic_link(html, custom_domain, market_listing_key)

    plaintext_link = find_magic_link_id_from_text(text, custom_domain, market_listing_key)

    if href_link, do: href_link, else: plaintext_link
  end

  def get_magic_link_id_from_announcement_html(_, _, _), do: nil

  defp random_string(length \\ 5) do
    length
    |> :crypto.strong_rand_bytes()
    |> Base.url_encode64()
    |> binary_part(0, length)
  end

  def convert_announcement_to_html(url) do
    case HTTPoison.get!(url, [], timeout: 60_000, recv_timeout: 60_000, follow_redirect: true) do
      %HTTPoison.Response{body: body} ->
        folder = "/tmp/#{random_string()}"
        File.mkdir!(folder)
        File.write("#{folder}/asx-announcement.pdf", body)

        {html, _status} =
          System.cmd("pdftohtml", [
            "#{folder}/asx-announcement.pdf",
            "-stdout",
            "-nodrm"
          ])

        File.rm_rf!(folder)

        html

      _ ->
        nil
    end
  end

  def convert_announcement_to_text(url) do
    case HTTPoison.get!(url, [], timeout: 60_000, recv_timeout: 60_000, follow_redirect: true) do
      %HTTPoison.Response{body: body} ->
        folder = "/tmp/#{random_string()}"
        File.mkdir!(folder)
        File.write("#{folder}/asx-announcement.pdf", body)

        {text, _status} =
          System.cmd("pdftotext", [
            "#{folder}/asx-announcement.pdf",
            "-",
            "-nodiag"
          ])

        File.rm_rf!(folder)

        text

      _ ->
        nil
    end
  end

  defp find_hub_href_magic_link(html, custom_domain, market_listing_key) do
    text =
      html
      |> Floki.parse_document!()
      |> Floki.find("a")
      |> Floki.attribute("href")
      |> Enum.join(" ")

    find_magic_link_id_from_text(text, custom_domain, market_listing_key)
  end

  defp find_magic_link_id_from_text(text, custom_domain, market_listing_key) do
    hub_url = if custom_domain, do: custom_domain.custom_domain

    base_domain =
      case Application.get_env(:helper, :runtime_env) do
        "development" ->
          # Using 3003 because we are referring to url for Hermes
          "localhost.com:3003"

        "staging" ->
          "fresh-staging.xyz"

        _ ->
          "investorhub.com"
      end

    text =
      text
      |> String.to_charlist()
      |> Enum.filter(&(&1 in 0..127))
      |> List.to_string()

    case Regex.run(~r/#{market_listing_key}.#{base_domain}\/link\/|#{hub_url}\/link\//i, text, return: :index) do
      [{start, len}] ->
        link_start = start + len
        link_end = link_start + 5
        String.slice(text, link_start..link_end)

      _ ->
        false
    end
  end

  def count_total_announcements_for_company(company_profile_id) do
    MediaAnnouncement
    |> join(:inner, [ma], m in Media, on: ma.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> Repo.aggregate(:count)
  end

  def count_total_announcements_for_company(company_profile_id, start_date, end_date) do
    MediaAnnouncement
    |> join(:inner, [ma], m in Media, on: ma.media_id == m.id and m.company_profile_id == ^company_profile_id)
    |> where(
      [ma],
      ma.posted_at >= ^start_date and ma.posted_at <= ^end_date
    )
    |> Repo.aggregate(:count)
  end

  def count_total_updates_for_company(company_profile_id) do
    MediaUpdate
    |> where([mu], mu.company_profile_id == ^company_profile_id)
    |> Repo.aggregate(:count)
  end

  def count_total_updates_for_company(company_profile_id, start_date, end_date) do
    MediaUpdate
    |> where([mu], mu.company_profile_id == ^company_profile_id)
    |> where([mu], mu.posted_at >= ^start_date and mu.posted_at <= ^end_date)
    |> Repo.aggregate(:count)
  end

  @doc """
  Returns the list of prepared_announcements.

  ## Examples

    iex> list_prepared_announcements()
    [%PreparedAnnouncement{}, ...]

  """
  def list_prepared_announcements do
    Repo.all(PreparedAnnouncement)
  end

  def list_non_draft_not_linked_prepared_announcements_for_company(company_profile_id, search \\ "") do
    case search do
      "" ->
        PreparedAnnouncement
        |> where(
          [pa],
          pa.company_profile_id == ^company_profile_id and
            not pa.is_draft and
            is_nil(pa.media_id)
        )
        |> order_by([pa], desc: pa.updated_at)
        |> Repo.all()

      _ ->
        PreparedAnnouncement
        |> where(
          [pa],
          pa.company_profile_id == ^company_profile_id and
            not pa.is_draft and
            is_nil(pa.media_id)
        )
        |> where([pa], ilike(pa.title, ^"%#{search}%"))
        |> order_by([pa], desc: pa.updated_at)
        |> Repo.all()
    end
  end

  @doc """
  Gets a single prepared_announcement.

  Raises `Ecto.NoResultsError` if the Prepared announcement does not exist.

  ## Examples

    iex> get_prepared_announcement!(123)
    %PreparedAnnouncement{}

    iex> get_prepared_announcement!(456)
    ** (Ecto.NoResultsError)

  """
  def get_prepared_announcement(id), do: Repo.get(PreparedAnnouncement, id)
  def get_prepared_announcement!(id), do: Repo.get!(PreparedAnnouncement, id)

  def get_prepared_announcement_by(opts), do: Repo.get_by(PreparedAnnouncement, opts)

  def get_prepared_announcement_by_hash_id(hash_id) do
    hash_id
    |> Helper.Hashid.decode_id()
    |> case do
      {:ok, [prepared_announcement_id]} when is_id?(prepared_announcement_id) ->
        get_prepared_announcement(prepared_announcement_id)

      _ ->
        nil
    end
  end

  @doc """
  Gets a single prepared_announcement that is not yet linked.

  ## Examples

    iex> get_unlinked_prepared_announcement(123)
    %PreparedAnnouncement{}

    iex> get_unlinked_prepared_announcement(456)
    nil

  """
  def get_unlinked_prepared_announcement(id) do
    PreparedAnnouncement
    |> where([pa], pa.id == ^id and not is_nil(pa.media_id))
    |> Repo.one()
  end

  def get_unlinked_prepared_announcement_by(attrs \\ []) do
    PreparedAnnouncement
    |> where([pa], is_nil(pa.media_id))
    |> where(^attrs)
    |> Repo.one()
  end

  @doc """
  Creates a prepared_announcement.

  ## Examples

    iex> create_prepared_announcement(%{field: value})
    {:ok, %PreparedAnnouncement{}}

    iex> create_prepared_announcement(%{field: bad_value})
    {:error, %Ecto.Changeset{}}

  """
  def create_prepared_announcement(attrs \\ %{}) do
    %PreparedAnnouncement{}
    |> PreparedAnnouncement.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a prepared_announcement.

  ## Examples

    iex> update_prepared_announcement(prepared_announcement, %{field: new_value})
    {:ok, %PreparedAnnouncement{}}

    iex> update_prepared_announcement(prepared_announcement, %{field: bad_value})
    {:error, %Ecto.Changeset{}}

  """
  def update_prepared_announcement(%PreparedAnnouncement{} = prepared_announcement, attrs) do
    prepared_announcement
    |> PreparedAnnouncement.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a prepared_announcement.

  ## Examples

    iex> delete_prepared_announcement(prepared_announcement)
    {:ok, %PreparedAnnouncement{}}

    iex> delete_prepared_announcement(prepared_announcement)
    {:error, %Ecto.Changeset{}}

  """
  def delete_prepared_announcement(%PreparedAnnouncement{} = prepared_announcement) do
    Repo.delete(prepared_announcement)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking prepared_announcement changes.

  ## Examples

    iex> change_prepared_announcement(prepared_announcement)
    %Ecto.Changeset{data: %PreparedAnnouncement{}}

  """
  def change_prepared_announcement(%PreparedAnnouncement{} = prepared_announcement, attrs \\ %{}) do
    PreparedAnnouncement.changeset(prepared_announcement, attrs)
  end

  def announcements_list_query(args) do
    announcements_query =
      MediaAnnouncement
      |> join(:inner, [ma], m in Media, on: ma.media_id == m.id)
      |> join(:full, [ma, _m], pa in PreparedAnnouncement, on: ma.media_id == pa.media_id)

    Enum.reduce(args, announcements_query, fn
      {:filters, filters}, query ->
        announcements_list_filter_with(query, filters)

      {:orders, orders}, query ->
        announcements_list_order_with(query, orders)

      _, query ->
        query
    end)
  end

  def announcements_list_filter_with(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        where(
          query,
          [ma, m, pa],
          m.company_profile_id == ^value or pa.company_profile_id == ^value
        )

      # since pa (prepared_announcements) don't have the fields listing_key and market_key, we do is_nil(pa.id) to ensure we don't lose the prepped announcements
      %{key: "listing_key", value: value}, query ->
        where(query, [ma, _m, pa], ma.listing_key == ^value or not is_nil(pa.id))

      %{key: "market_key", value: value}, query ->
        where(query, [ma, _m, pa], ma.market_key == ^value or not is_nil(pa.id))

      %{key: "search", value: search_phrase}, query ->
        announcements_list_filter_by_search(query, search_phrase)

      %{key: "posted_at_greater_than", value: value}, query ->
        where(query, [ma, _m, pa], ma.posted_at > ^value or pa.inserted_at > ^value)

      %{key: "posted_at_less_than", value: value}, query ->
        where(query, [ma, _m, pa], ma.posted_at < ^value or pa.inserted_at < ^value)

      %{key: "is_live_only", value: "true"}, query ->
        where(query, [ma, _m, pa], not is_nil(ma.id) and (not pa.is_draft or is_nil(pa.id)))

      %{key: "is_live_only", value: "false"}, query ->
        query

      %{key: "ai_summaries", value: "true"}, query ->
        where(query, [ma, _m, pa], is_nil(ma.summary) or not is_nil(ma.summary_ai))

      %{key: "announcement_status", value: announcement_status}, query ->
        announcements_list_query_apply_announcement_status(
          query,
          announcement_status
        )

      %{key: "has_active_question", value: "true"}, query ->
        announcements_list_query_has_active_questions_filter(query)

      %{key: "tags", value: tags}, query ->
        MediaQueries.tags_filter_query(query, tags)

      %{key: _, value: _}, query ->
        query
    end)
  end

  def announcements_list_order_with(query, order) do
    Enum.reduce(order, query, fn
      %{key: "id", value: "desc"}, query ->
        order_by(query, [ma, _m, pa], desc_nulls_first: ma.id)

      %{key: "id", value: "asc"}, query ->
        order_by(query, [ma, _m, pa], asc_nulls_first: ma.id)

      %{key: "is_draft", value: "true"}, query ->
        order_by(query, [ma, _m, pa], desc_nulls_last: pa.is_draft)

      %{key: "is_draft", value: "false"}, query ->
        order_by(query, [ma, _m, pa], asc_nulls_last: pa.is_draft)

      %{key: "inserted_at", value: "desc"}, query ->
        order_by(query, [ma, _m, pa], desc_nulls_last: pa.inserted_at)

      %{key: "inserted_at", value: "asc"}, query ->
        order_by(query, [ma, _m, pa], asc_nulls_last: pa.inserted_at)

      %{key: "updated_at", value: "desc"}, query ->
        order_by(query, [ma, _m, pa], desc_nulls_last: pa.updated_at)

      %{key: "updated_at", value: "asc"}, query ->
        order_by(query, [ma, _m, pa], asc_nulls_last: pa.updated_at)

      %{key: "posted_at", value: "desc"}, query ->
        order_by(query, [ma, _m, pa], desc_nulls_first: ma.posted_at)

      %{key: "posted_at", value: "asc"}, query ->
        order_by(query, [ma, _m, pa], asc_nulls_first: ma.posted_at)

      %{key: _, value: _}, query ->
        query
    end)
  end

  defp announcements_list_filter_by_search(query, "") do
    query
  end

  defp announcements_list_filter_by_search(query, search_phrase) do
    where(
      query,
      [ma, t, pa],
      ilike(ma.header, ^"%#{search_phrase}%") or ilike(pa.title, ^"%#{search_phrase}%")
    )
  end

  defp announcements_list_query_apply_announcement_status(query, "") do
    query
  end

  defp announcements_list_query_apply_announcement_status(query, announcement_status) do
    search_announcement_status = String.split(announcement_status, ",")

    is_draft_announcement_included = Enum.member?(search_announcement_status, "draft")

    is_prepared_announcement_included = Enum.member?(search_announcement_status, "prepared")

    is_live_announcement_included = Enum.member?(search_announcement_status, "live")

    where(
      query,
      [ma, t, pa],
      fragment(
        "(? AND ?) OR (? AND ?) OR (? AND ?)",
        ^is_draft_announcement_included,
        pa.is_draft,
        ^is_prepared_announcement_included,
        not pa.is_draft and is_nil(ma.id),
        ^is_live_announcement_included,
        not is_nil(ma.id) and (not pa.is_draft or is_nil(pa.id))
      )
    )
  end

  defp announcements_list_query_has_active_questions_filter(query) do
    answered_parent_comment_ids_query =
      MediaComment
      |> where([mc], not is_nil(mc.parent_id))
      |> group_by([mc], mc.parent_id)
      |> select([mc], mc.parent_id)

    query
    |> join(:inner, [q], media in assoc(q, :media))
    |> join(:inner, [..., media], comments in assoc(media, :comments))
    |> where(
      [..., comments],
      is_nil(comments.parent_id) and is_nil(comments.company_author_id) and
        comments.id not in subquery(answered_parent_comment_ids_query) and
        not comments.done
    )
    |> distinct([q], q.id)
  end

  def convert_pdf_to_html(url) do
    Task.await(
      Task.async(fn -> convert_announcement_to_html(url) end),
      60_000
    )
  catch
    _ -> nil
    :exit, _reason -> nil
  end

  def convert_pdf_to_text(url) do
    Task.await(
      Task.async(fn -> convert_announcement_to_text(url) end),
      60_000
    )
  catch
    _ -> nil
    :exit, _reason -> nil
  end

  def bulk_add_announcements_html_and_text(num) do
    now = NaiveDateTime.utc_now(:second)

    start_date = Timex.shift(now, weeks: -num)

    MediaAnnouncement
    |> where([ma], is_nil(ma.pdf_as_text) and not is_nil(ma.media_id))
    |> where([ma], ma.inserted_at >= ^start_date)
    |> Repo.all(timeout: 300_000)
    |> Enum.chunk_every(3_000)
    |> Enum.each(&add_pdf_as_html_and_pdf_as_text_into_announcements(&1))
  end

  def add_pdf_as_html_and_pdf_as_text_into_announcements([%MediaAnnouncement{} | _] = announcements) do
    Enum.each(
      announcements,
      &Gaia.Jobs.ManualAddAnnouncementHtmlAndText.enqueue(%{media_announcement_id: &1.id})
    )
  end

  def add_pdf_as_html_and_pdf_as_text_into_announcements(_) do
    :skip
  end

  def add_pdf_as_html_and_pdf_as_text_into_announcement(%MediaAnnouncement{url: url} = announcement) do
    text = convert_pdf_to_text(url)
    update_media_announcement_pdf_and_text(announcement, %{pdf_as_text: text})
  end

  def add_pdf_as_html_and_pdf_as_text_into_announcement(_) do
    :skip
  end

  def upsert_media_update_with_content_and_attachments_and_comment_content_and_title(
        %{company_user_id: company_user_id, attachments: attachments, title: title, slug: slug} = args,
        %MediaUpdate{id: media_update_id, media_id: media_id, is_draft: is_draft} = existing_media_update
      ) do
    args =
      Map.merge(args, %{media_update_id: media_update_id, media_id: media_id, is_draft: is_draft})

    included_types = get_included_attachment_types(attachments)

    attrs = %{
      included_types: included_types,
      last_updated_by_id: company_user_id,
      title: title,
      slug: slug
    }

    Ecto.Multi.new()
    |> Ecto.Multi.run(:update_media_update, fn _repo, _changes ->
      update_media_update(existing_media_update, attrs)
    end)
    |> maybe_upsert_media_update_content(args)
    |> maybe_upsert_media_update_attachments(args)
    |> maybe_upsert_media_update_comment_content(args)
    |> Repo.transaction(timeout: 600_000)
  end

  def get_included_attachment_types([]) do
    [:none]
  end

  def get_included_attachment_types(attachments) do
    attachments |> Enum.group_by(& &1.type) |> Enum.map(fn {key, _value} -> key end)
  end

  def maybe_upsert_media_update_content(multi, %{content: "", comment_content: ""}) do
    multi
  end

  def maybe_upsert_media_update_content(multi, %{
        content: content,
        comment_content: comment_content,
        company_user_id: company_user_id,
        media_update_id: media_update_id
      }) do
    media_update_content_input = %MediaUpdateContent{
      content: content,
      comment_content: comment_content,
      updated_by_id: company_user_id,
      media_update_id: media_update_id,
      inserted_at: NaiveDateTime.utc_now(:second),
      updated_at: NaiveDateTime.utc_now(:second)
    }

    Ecto.Multi.insert(multi, :upsert_media_update_content, media_update_content_input,
      on_conflict: {:replace_all_except, [:id, :media_update_id, :inserted_at]},
      conflict_target: {:unsafe_fragment, ~s<("media_update_id") WHERE invalidated IS FALSE>}
    )
  end

  def maybe_upsert_media_update_attachments(multi, %{attachments: [], media_update_id: media_update_id}) do
    maybe_delete_media_update_attachments(multi, media_update_id, 0)
  end

  def maybe_upsert_media_update_attachments(multi, %{attachments: attachments, media_update_id: media_update_id}) do
    total_attachments_count = length(attachments)

    attachments_input =
      Enum.map(
        attachments,
        &%{
          type: &1.type,
          thumbnail_url: maybe_put_thumbnail_url_for_update_attachment(&1),
          url: &1.url,
          media_update_id: media_update_id,
          title: Map.get(&1, :title),
          description: Map.get(&1, :description),
          order_id: &1.order_id,
          inserted_at: NaiveDateTime.utc_now(:second),
          updated_at: NaiveDateTime.utc_now(:second)
        }
      )

    multi
    |> maybe_delete_media_update_attachments(media_update_id, total_attachments_count)
    |> Ecto.Multi.insert_all(
      :upsert_attachments,
      MediaUpdateAttachment,
      attachments_input,
      on_conflict: {:replace_all_except, [:id, :inserted_at]},
      conflict_target: {:unsafe_fragment, ~s<("media_update_id", "order_id") WHERE invalidated IS NOT TRUE>}
    )
  end

  defp maybe_delete_media_update_attachments(multi, media_update_id, total_attachments_count) do
    delete_attachments_query =
      where(
        MediaUpdateAttachment,
        [mua],
        mua.media_update_id == ^media_update_id and mua.order_id > ^total_attachments_count
      )

    Ecto.Multi.delete_all(multi, :delete_attachments, delete_attachments_query, with_invalidated: true)
  end

  def maybe_put_thumbnail_url_for_update_attachment(%{type: :video, thumbnail_url: thumbnail_url})
      when not is_nil(thumbnail_url) do
    thumbnail_url
  end

  def maybe_put_thumbnail_url_for_update_attachment(%{type: :video, url: url}) do
    with {:ok, %URI{host: host, path: path}} <- URI.new(url),
         id when is_binary(id) <-
           path
           |> String.split("/")
           |> List.last(),
         thumbnail_url when is_binary(thumbnail_url) <-
           MediaUpdateAttachment.get_thumbnail_url(host, id, url) do
      thumbnail_url
    else
      _ ->
        nil
    end
  end

  def maybe_put_thumbnail_url_for_update_attachment(%{url: url, thumbnail_url: nil, id: id, type: :pdf}) do
    Gaia.Jobs.CreatePDFAttachmentThumbnail.enqueue(%{attachment_id: id, url: url})

    nil
  end

  def maybe_put_thumbnail_url_for_update_attachment(_), do: nil

  def maybe_upsert_media_update_comment_content(multi, %{is_draft: true, comment_content: _comment_content}) do
    multi
  end

  def maybe_upsert_media_update_comment_content(multi, %{is_draft: _, comment_content: "", media_id: media_id}) do
    delete_all_existing_company_update_comments(multi, media_id)
  end

  def maybe_upsert_media_update_comment_content(multi, %{
        comment_content: comment_content,
        company_user_id: company_user_id,
        media_id: media_id
      }) do
    media_update_comment_content_input = %MediaComment{
      company_author_id: company_user_id,
      media_id: media_id,
      content: comment_content,
      inserted_at: NaiveDateTime.utc_now(:second),
      updated_at: NaiveDateTime.utc_now(:second)
    }

    multi
    |> delete_all_existing_company_update_comments(media_id)
    |> Ecto.Multi.insert(:upsert_media_update_comment_content, media_update_comment_content_input)
  end

  def delete_all_existing_company_update_comments(multi, media_id) do
    delete_query =
      where(
        MediaComment,
        [mc],
        mc.media_id == ^media_id and is_nil(mc.investor_user_id) and is_nil(mc.parent_id) and
          not is_nil(mc.company_author_id)
      )

    Ecto.Multi.delete_all(multi, :delete_all_existing_company_update_comments, delete_query)
  end

  def bulk_add_announcement_missing_thumbnail_images_for_companies do
    Enum.each(
      Companies.list_company_profile_ids(),
      &bulk_add_announcement_missing_thumbnail_images_for_company(&1)
    )
  end

  def bulk_add_announcement_missing_thumbnail_images_for_company(company_profile_id) do
    MediaAnnouncement
    |> join(:inner, [ma], m in assoc(ma, :media))
    |> where([ma, m], m.company_profile_id == ^company_profile_id and is_nil(ma.thumbnail))
    |> select([ma, m], %{announcement_id: ma.id, url: ma.url})
    |> Repo.all(timeout: 300_000)
    |> Enum.chunk_every(3_000)
    |> Enum.each(&add_thumbnail_images_into_announcements(&1))
  end

  def bulk_regenerate_thumbnail_images_of_announcements_for_companies(start_date, end_date) do
    Enum.each(
      Companies.list_company_profile_ids(),
      &bulk_regenerate_thumbnail_images_of_announcements_for_company(&1, start_date, end_date)
    )
  end

  def bulk_regenerate_thumbnail_images_of_announcements_for_company(company_profile_id, start_date, end_date) do
    MediaAnnouncement
    |> join(:inner, [ma], m in assoc(ma, :media))
    |> where(
      [ma, m],
      m.company_profile_id == ^company_profile_id and ma.updated_at >= ^start_date and
        ma.updated_at <= ^end_date
    )
    |> select([ma, m], %{announcement_id: ma.id, url: ma.url})
    |> Repo.all(timeout: 300_000)
    |> Enum.chunk_every(3_000)
    |> Enum.each(&add_thumbnail_images_into_announcements(&1))
  end

  def add_thumbnail_images_into_announcements([%{announcement_id: _announcement_id, url: _url} | _] = announcements) do
    Enum.each(announcements, &Gaia.Jobs.CreateAnnouncementThumbnail.enqueue(&1))
  end

  def add_thumbnail_images_into_announcements(_) do
    :skip
  end

  def manually_add_media_updates_included_types do
    MediaUpdate
    |> Repo.all()
    |> Enum.each(
      &(&1
        |> Repo.preload(:attachments)
        |> manually_add_media_update_included_types())
    )
  end

  def manually_add_media_update_included_types(%MediaUpdate{attachments: attachments} = existing_media_update) do
    included_types = get_included_attachment_types(attachments)

    update_media_update(existing_media_update, %{included_types: included_types})
  end

  def manually_add_media_update_included_types(_), do: :skip

  def bulk_add_media_update_attachment_thumbnail_url do
    MediaUpdateAttachment
    |> where([mua], is_nil(mua.thumbnail_url))
    |> where([mua], is_nil(mua.thumbnail))
    |> where([mua], mua.type == :pdf)
    |> Repo.all()
    |> Enum.each(
      &update_media_update_attachment(&1, %{
        thumbnail_url: maybe_put_thumbnail_url_for_update_attachment(&1)
      })
    )
  end

  def spp_announcements_to_scrape(market_key) do
    MediaAnnouncement
    |> join(:left, [ma], spp in RaisesSppHistorical, on: ma.id == spp.media_announcement_id)
    |> where(
      [ma],
      ilike(ma.header, ^"%SPP%") or ilike(ma.header, ^"%Share%Purch%Plan%") or
        ilike(ma.header, ^"%Sec%Purch%Plan%")
    )
    |> where([ma], ma.market_key == ^market_key)
    |> where([ma, spp], is_nil(spp.media_announcement_id))
    |> select([ma], ma.id)
    |> Repo.all()
  end

  def media_comments_query(args) do
    Enum.reduce(args, MediaComment, fn
      {:filters, filters}, query ->
        media_comments_filter_with(query, filters)

      {:orders, orders}, query ->
        media_comments_order_with(query, orders)

      _, query ->
        query
    end)
  end

  defp media_comments_filter_with(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        from(q in query,
          join: m in assoc(q, :media),
          where: m.company_profile_id == ^value
        )

      %{key: "type", value: "question"}, query ->
        from(q in query, where: is_nil(q.parent_id))

      %{key: "type", value: "reply"}, query ->
        from(q in query, where: not is_nil(q.parent_id))

      %{key: "author", value: "investor"}, query ->
        from(q in query, where: not is_nil(q.investor_user_id))

      %{key: "author", value: "company"}, query ->
        from(q in query, where: not is_nil(q.company_author_id))

      %{key: "is_private", value: "false"}, query ->
        from(q in query, where: not q.private)

      %{key: "is_private", value: "true"}, query ->
        from(q in query, where: q.private)

      %{key: "category", value: "default"}, query ->
        query

      %{key: "category", value: "starred"}, query ->
        where(query, [q], q.id in subquery(starred_media_comments_query()))

      %{key: _, value: _}, query ->
        query
    end)
  end

  defp media_comments_popular_sort_query(query) do
    query
    |> join(
      :left,
      [q],
      media_likes in subquery(
        MediaLike
        |> where([ml], ml.like)
        |> group_by([ml], ml.media_id)
        |> select([ml], %{
          media_id: ml.media_id,
          count: count(ml.id)
        })
      ),
      on: q.media_id == media_likes.media_id
    )
    |> join(
      :left,
      [q, media_likes],
      announcement_views in subquery(
        InvestorHub
        |> where([ih], like(ih.event, "%_announcement_page_viewed"))
        |> where([ih], fragment("split_part(?, '_', 1) ~ '^\\d+$'", ih.event))
        |> join(:inner, [ih], a in MediaAnnouncement,
          on: a.id == fragment("cast(split_part(?, '_', 1) as bigint)", ih.event)
        )
        |> where([ih, a], not is_nil(a.media_id))
        |> group_by([ih, a], a.id)
        |> select([ih, a], %{
          count: count(ih.id),
          media_id: a.media_id
        })
      ),
      on: q.media_id == announcement_views.media_id
    )
    |> join(
      :left,
      [q, media_likes, announcement_views],
      update_views in subquery(
        InvestorHub
        |> where([ih], like(ih.event, "%_update_page_viewed"))
        |> where([ih], fragment("split_part(?, '_', 1) ~ '^\\d+$'", ih.event))
        |> join(:inner, [ih], u in MediaUpdate, on: u.id == fragment("cast(split_part(?, '_', 1) as bigint)", ih.event))
        |> where([ih, u], not is_nil(u.media_id))
        |> group_by([ih, u], u.id)
        |> select([ih, u], %{
          count: count(ih.id),
          media_id: u.media_id
        })
      ),
      on: q.media_id == update_views.media_id
    )
    |> join(
      :left,
      [q, media_likes, announcement_views, update_views],
      children_likes in subquery(
        MediaComment
        |> join(:left, [mc], children in MediaComment, on: children.parent_id == mc.id and not children.invalidated)
        |> group_by([mc, children], mc.media_id)
        |> select([mc, children], %{
          media_id: mc.media_id,
          total_children_likes: sum(children.likes)
        })
      ),
      on: q.media_id == children_likes.media_id
    )
    |> order_by(
      [q, _, media_likes, announcement_views, update_views, children_likes],
      desc: [
        coalesce(children_likes.total_children_likes, 0),
        q.likes,
        coalesce(media_likes.count, 0)
      ],
      desc: coalesce(announcement_views.count, 0) + coalesce(update_views.count, 0),
      desc: q.inserted_at
    )
  end

  defp starred_media_comments_query do
    MediaCommentStar
    |> where([mcs], mcs.starred)
    |> select([mcs], mcs.media_comment_id)
    |> distinct(true)
  end

  @doc """
  Gets a single tag.

  Returns nil if the Tag does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_media_tag_by(%{key: value})
      %Contact{}

      iex> get_media_tag_by(%{key: value})
      nil

      iex> get_media_tag_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_media_tag_by(attrs), do: Repo.get_by(MediaTag, attrs)

  @doc """
  Creates a tag.

  ## Examples

      iex> create_media_tag(%{field: value})
      {:ok, %MediaTag{}}

      iex> create_media_tag(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_media_tag(attrs \\ %{}) do
    %MediaTag{}
    |> MediaTag.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a tag.

  ## Examples

      iex> update_media_tag(tag, %{field: new_value})
      {:ok, %Tag{}}

      iex> update_media_tag(tag, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_media_tag(%MediaTag{} = tag, attrs) do
    tag
    |> MediaTag.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a list of maps.

  It includes all created tags for announcements and updates, ordered by most used.

  ## Examples

    iex> existing_tags(profile)
    [%{id: "id", invalidated: false, name: "name"}]

  """
  def existing_tags(%Profile{id: company_profile_id}) do
    MediaTag
    |> join(:inner, [t], c in assoc(t, :media))
    |> where(company_profile_id: ^company_profile_id)
    |> group_by([t], [t.name])
    |> order_by([t], desc: count(t.name))
    |> order_by([t], asc: t.name)
    |> select([t], %{id: fragment("'tag-' || ?", t.name), invalidated: false, name: t.name})
    |> Repo.all()
  end

  def existing_tags(_), do: []

  @doc """
  Pull all medias
  """
  def get_all_medias_per_company(company_profile_id) do
    # Step 1: Pull all media and make a cross join with all users
    all_users =
      User
      |> where([i], i.company_profile_id == ^company_profile_id)
      |> select([i], %{investor_user_id: i.id})

    all_anns =
      Media
      |> join(:inner, [m], ima in MediaAnnouncement, on: m.id == ima.media_id)
      |> select([m, ima], %{
        media_id: m.id,
        announcement_id: ima.id,
        update_id: nil,
        media_type: ^"announcement",
        posted_at: ima.posted_at
      })
      |> where([m], m.company_profile_id == ^company_profile_id)

    all_updates =
      Media
      |> join(:inner, [m], imu in MediaUpdate, on: m.id == imu.media_id)
      |> select([m, imu], %{
        media_id: m.id,
        announcement_id: nil,
        update_id: imu.id,
        media_type: ^"update",
        posted_at: imu.posted_at
      })
      |> where([m], m.company_profile_id == ^company_profile_id)

    all_media =
      all_anns
      |> union_all(^all_updates)
      |> subquery()
      |> join(:inner, [m], u in subquery(all_users), on: true)
      |> select([m, u], %{
        media_id: m.media_id,
        investor_user_id: u.investor_user_id,
        announcement_id: m.announcement_id,
        update_id: m.update_id,
        media_type: m.media_type,
        posted_at: m.posted_at
      })

    all_media
  end

  @doc """
  Get all media pieces and actions associated to those actions.
  """
  def get_actions_data(company_profile_id) do
    # Step 1: Pull all media and make a cross join with all users
    all_media = get_all_medias_per_company(company_profile_id)

    # Step 2: Calculate rates of usage
    likes =
      all_media
      |> subquery()
      |> join(:inner, [m], ml in MediaLike, on: m.media_id == ml.media_id and m.investor_user_id == ml.investor_user_id)
      |> select([m, ml], %{
        media_id: m.media_id,
        investor_user_id: m.investor_user_id,
        event_type:
          fragment(
            "'like' || ':' || case when ? = true then 'like' when ? = false then 'dislike' else 'none' end",
            ml.like,
            ml.like
          ),
        event_time: coalesce(ml.inserted_at, m.posted_at)
      })

    comments =
      all_media
      |> subquery()
      |> join(:inner, [m], mc in MediaComment,
        on:
          m.media_id == mc.media_id and not is_nil(mc.investor_user_id) and
            m.investor_user_id == mc.investor_user_id
      )
      |> select([m, mc], %{
        media_id: m.media_id,
        investor_user_id: m.investor_user_id,
        event_type:
          fragment(
            "'comment' || ':' || case when ? is not null then 'yes' else 'none' end",
            mc.id
          ),
        event_time: coalesce(mc.inserted_at, m.posted_at)
      })

    comment_likes =
      all_media
      |> subquery()
      |> join(:inner, [m], mc in MediaComment, on: mc.media_id == m.media_id)
      |> join(:inner, [m, mc], mcl in MediaCommentLike,
        on:
          mc.id == mcl.announcement_comment_id and not is_nil(mcl.investor_user_id) and
            m.investor_user_id == mcl.investor_user_id
      )
      |> select([m, mc, mcl], %{
        media_id: m.media_id,
        investor_user_id: m.investor_user_id,
        event_type:
          fragment(
            "'comment_like' || ':' || case when ? = true then 'like' when ? = false then 'dislike' else 'none' end",
            mcl.like,
            mcl.like
          ),
        event_time: coalesce(mcl.inserted_at, m.posted_at)
      })

    surveys =
      all_media
      |> subquery()
      |> join(:inner, [m], msa in MediaSurveyAnswer,
        on:
          m.media_id == msa.media_id and not is_nil(msa.investor_user_id) and
            m.investor_user_id == msa.investor_user_id
      )
      |> select([m, msa], %{
        media_id: m.media_id,
        investor_user_id: m.investor_user_id,
        event_type:
          fragment(
            "'survey' || ':' || case when ? is not null then cast(? as text) else 'none' end || ':' || case when ? is not null then cast(? as text) else 'none' end",
            msa.investor_user_id,
            msa.question,
            msa.investor_user_id,
            msa.answer
          ),
        event_time: coalesce(msa.inserted_at, m.posted_at)
      })

    # HACK - This is a hack to get the views because credo doesn't like how complicated the query is
    views = get_all_view_actions(all_media)

    # All actions and non-actions
    all_actions =
      views
      |> union_all(^likes)
      |> union_all(^comments)
      |> union_all(^comment_likes)
      |> union_all(^surveys)

    {all_actions, all_media}
  end

  @doc """
  HACK - This is a hack to get mix credo to work
  """
  def get_all_view_actions(all_media) do
    views =
      InvestorHub
      |> where(
        [ih],
        ilike(ih.event, "%_announcement_page_viewed") or ilike(ih.event, "%_update_page_viewed")
      )
      |> select([ih], %{
        id: type(fragment("split_part(?, '_', 1)", ih.event), :integer),
        media_type:
          fragment(
            "CASE WHEN ? THEN 'announcement' ELSE 'update' END",
            ilike(ih.event, "%_announcement_page_viewed")
          ),
        investor_user_id: ih.investor_user_id,
        event_type: fragment("'view' || ':' || split_part(?, '_', 1)", ih.event),
        event_time: ih.inserted_at
      })
      |> subquery()
      |> join(:inner, [ih, am], am in subquery(all_media),
        on:
          ((am.announcement_id == ih.id and am.media_type == "announcement") or
             (am.update_id == ih.id and am.media_type == "update")) and
            not is_nil(ih.investor_user_id) and am.investor_user_id == ih.investor_user_id
      )
      |> select([ih, am], %{
        media_id: am.media_id,
        investor_user_id: ih.investor_user_id,
        event_type:
          fragment(
            "case when ? is not null then ? else 'view:none' end",
            ih.event_type,
            ih.event_type
          ),
        event_time: coalesce(ih.event_time, am.posted_at)
      })

    views
  end

  @doc """
  Calculate the usage rates for a company profile.
  Return the rates and also the actions/non-actions for further analysis.
  """
  def action_rates_by_company_profile(company_profile_id) do
    # Step 1: Collect all media pieces for the company
    {all_actions, all_media} = get_actions_data(company_profile_id)

    # Create media x user x event_type table
    # Join on all actions and require that the event_type is not the same as the action in question
    # Remaining should be every instance an action wasn't taken
    non_actions =
      all_media
      |> subquery()
      |> join(
        :cross,
        t in fragment(
          "select unnest(?::text[]) as event_type",
          ^["like", "view", "like", "survey", "comment", "comment_like"]
        )
      )
      |> select([am, t], %{
        media_id: am.media_id,
        investor_user_id: am.investor_user_id,
        event_type: t.event_type,
        event_time: am.posted_at
      })
      |> subquery()
      |> join(:left, [am], aa in subquery(all_actions),
        on:
          am.media_id == aa.media_id and
            am.investor_user_id == aa.investor_user_id and
            fragment("split_part(?, ':', 1)", aa.event_type) == am.event_type
      )
      |> where([am, aa], is_nil(aa.event_type))
      |> select([am, aa], %{
        media_id: am.media_id,
        investor_user_id: am.investor_user_id,
        event_type: fragment("? || ':' || 'none'", am.event_type),
        event_time: am.event_time
      })

    total_users =
      User
      |> where([i], i.company_profile_id == ^company_profile_id)
      |> select([i], %{total_users: count()})
      |> Repo.one()
      |> Map.get(:total_users)

    # Step 2: Calculate the usage rates
    # Calculate the usage rates
    rates =
      all_actions
      |> subquery()
      |> group_by([a], [a.media_id, fragment("split_part(?, ':', 1)", a.event_type)])
      |> select([a], %{
        media_id: a.media_id,
        event_type: fragment("split_part(?, ':', 1)", a.event_type),
        event_time: fragment("max(?)", a.event_time),
        rate: type(count(a.investor_user_id, :distinct), :float) / type(^total_users, :float)
      })

    {all_actions, non_actions, rates}
  end

  @doc """
  Creates a social post.

  ## Examples

      iex> create_social_post(%{field: value})
      {:ok, %SocialPost{}}

      iex> create_social_post(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_social_post(attrs \\ %{}) do
    %SocialPost{}
    |> SocialPost.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a social post.

  ## Examples

      iex> update_social_post(social_post, %{field: new_value})
      {:ok, %SocialPost{}}

      iex> update_social_post(social_post, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_social_post(%SocialPost{} = social_post, attrs) do
    social_post
    |> SocialPost.changeset(attrs)
    |> Repo.update()
  end

  def upsert_social_post(social_post, attrs) do
    case social_post do
      %SocialPost{} = existing_social_post ->
        update_social_post(existing_social_post, attrs)

      _ ->
        create_social_post(attrs)
    end
  end

  def publish_social_post(
        %Media{company_profile: %Profile{social_connection: social_connection}},
        %SocialPost{platform: platform, social_post_id: social_post_id, published_at: published_at} = social_post
      ) do
    with {:check_published, false} <-
           {:check_published, published_to_social?(social_post_id, published_at)},
         {:check_social_connected, true} <-
           {:check_social_connected, social_connected?(social_connection, platform)},
         {:publish_social_post, {:ok, %SocialPost{} = published_social_post}} <-
           {:publish_social_post,
            publish_to_social(
              social_connection,
              social_post
            )} do
      {:ok, published_social_post}
    else
      error ->
        {:error, error}
    end
  end

  defp published_to_social?(social_post_id, published_at) when is_nil(social_post_id) or is_nil(published_at), do: false

  defp published_to_social?(_social_post_id, _published_at), do: true

  defp social_connected?(social_connection, :linkedin) do
    Companies.get_is_linkedin_setup_completed(social_connection)
  end

  defp social_connected?(social_connection, :twitter) do
    Companies.get_is_twitter_setup_completed(social_connection)
  end

  # With content_formatted field, we no longer need complex formatting logic

  # Simple pass-through for content formatting
  # The formatted content is now generated on the frontend and stored in content_formatted
  defp format_content_to_post(post_content, _) when is_binary(post_content), do: {:ok, post_content}

  # Handle nil content_formatted - provide a default empty string
  defp format_content_to_post(nil, _), do: {:ok, ""}

  defp publish_to_social(
         %SocialConnection{linkedin_access_token: access_token, linkedin_organisation_id: organisation_id},
         %SocialPost{platform: :linkedin, content_formatted: content_formatted, attachments: attachments} = social_post
       ) do
    # Use content_formatted directly
    with {:format_content_to_post, {:ok, formatted_content_to_post}} <-
           {:format_content_to_post, format_content_to_post(content_formatted, :linkedin)},
         {:post_to_linkedin, {:ok, post_id}} <-
           {:post_to_linkedin,
            LinkedIn.share_post_to_feed_with_attachments(
              access_token,
              formatted_content_to_post,
              organisation_id,
              attachments
            )},
         # Create the published URL for LinkedIn
         published_url = "https://www.linkedin.com/feed/update/#{post_id}",
         {:update_social_post, {:ok, %SocialPost{} = published_social_post}} <-
           {:update_social_post,
            upsert_social_post(social_post, %{
              social_post_id: post_id,
              published_at: NaiveDateTime.utc_now(:second),
              published_url: published_url,
              status: :published
            })} do
      {:ok, published_social_post}
    end
  end

  defp publish_to_social(
         %SocialConnection{twitter_oauth_token: token, twitter_oauth_token_secret: token_secret},
         %SocialPost{platform: :twitter, content_formatted: content_formatted, attachments: attachments} = social_post
       ) do
    # Use content_formatted directly
    with {:format_content_to_post, {:ok, formatted_post_content}} <-
           {:format_content_to_post, format_content_to_post(content_formatted, :twitter)},
         {:post_to_twitter, {:ok, %{"data" => %{"id" => post_id}}}} <-
           {:post_to_twitter,
            Twitter.new_tweet_with_attachments(
              formatted_post_content,
              token,
              token_secret,
              attachments
            )},
         # Create the published URL for Twitter
         published_url = "https://x.com/twitter/status/#{post_id}",
         {:update_social_post, {:ok, %SocialPost{} = published_social_post}} <-
           {:update_social_post,
            upsert_social_post(social_post, %{
              social_post_id: post_id,
              published_at: NaiveDateTime.utc_now(:second),
              published_url: published_url,
              status: :published
            })} do
      {:ok, published_social_post}
    end
  end

  defp log_announcement_error(error, symbol, pdf_link, posted_at) do
    context = %{
      metadata: %{
        "listing_key" => symbol,
        "pdf_link" => pdf_link,
        "posted_at" => inspect(posted_at)
      },
      name: "Announcements Error"
    }

    Helper.Error.Custom.ErrorHandler.handle("Announcement failed to save", context, error)
  end

  def get_social_post_by(attrs) do
    Repo.get_by(SocialPost, attrs)
  end

  def update_or_publish_media_update(media_update, posted_by_id) do
    case media_update do
      %MediaUpdate{is_draft: true, posted_at: nil} ->
        publish_media_update(Repo.preload(media_update, [:media]), posted_by_id)

      _ ->
        update_media_update(media_update, %{
          content_published: media_update.content_draft,
          posted_by_id: posted_by_id
        })
    end
  end

  def publish_media_update(
        %MediaUpdate{media: %Media{company_profile_id: company_profile_id}} = media_update,
        posted_by_id
      ) do
    media_update
    |> update_media_update(%{
      content_published: media_update.content_draft,
      is_draft: false,
      posted_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second),
      posted_by_id: posted_by_id
    })
    |> case do
      {:ok, %MediaUpdate{} = updated_media_update} ->
        updated_media_update
        |> Repo.preload(:content)
        |> maybe_add_media_update_comment_content(posted_by_id)

        updated_media_update |> Repo.preload(:attachments) |> maybe_add_hub_notifications()

        Task.start(fn ->
          send_activity_update_posted_email_to_profile_admins(
            company_profile_id,
            updated_media_update
          )
        end)

        Task.start(fn -> Distribution.enqueue_update_distributions(updated_media_update) end)

        {:ok, updated_media_update}

      error ->
        error
    end
  end

  defp maybe_add_media_update_comment_content(
         %MediaUpdate{
           is_draft: is_draft,
           media_id: media_id,
           content: %MediaUpdateContent{comment_content: comment_content} = _media_update_content
         } = _media_update,
         current_user_id
       )
       when not is_nil(comment_content) do
    args = %{
      company_user_id: current_user_id,
      comment_content: comment_content,
      is_draft: is_draft,
      media_id: media_id
    }

    Ecto.Multi.new()
    |> maybe_upsert_media_update_comment_content(args)
    |> Repo.transaction(timeout: 600_000)
  end

  defp maybe_add_media_update_comment_content(_, _), do: :ok

  defp maybe_add_hub_notifications(
         %MediaUpdate{media: %Media{id: media_id}, included_types: included_types, attachments: attachments} =
           _media_update
       )
       when is_list(included_types) do
    included_types
    |> Enum.member?(:video)
    |> case do
      true ->
        attachments
        |> Enum.find(&(&1.type == :video and is_binary(&1.url)))
        |> maybe_enqueue_create_video_hub_notifications_for_company_investors_job(media_id)

      false ->
        :ok
    end
  end

  defp maybe_add_hub_notifications(_) do
    :ok
  end

  defp maybe_enqueue_create_video_hub_notifications_for_company_investors_job(%MediaUpdateAttachment{}, media_id) do
    Task.start(fn ->
      Gaia.Jobs.CreateVideoHubNotificationsForCompanyInvestors.enqueue(%{
        "media_id" => media_id
      })
    end)
  end

  defp maybe_enqueue_create_video_hub_notifications_for_company_investors_job(_, _), do: :ok

  # Send emails to all company admin users whose distribution notification preference is on
  defp send_activity_update_posted_email_to_profile_admins(company_profile_id, media_update) do
    %{
      some_configured: some_configured,
      email_campaign_status: email_campaign_status,
      linkedin_post_status: linkedin_post_status,
      twitter_post_status: twitter_post_status
    } = get_configurations_for_media(company_profile_id, media_update)

    company_profile_id
    |> Gaia.Companies.active_admin_users_for_distribution_email(:update)
    |> Enum.each(fn admin_user ->
      res =
        Gaia.Notifications.Email.deliver(
          EmailTransactional.Company,
          :activity_update_posted,
          [
            admin_user,
            media_update,
            email_campaign_status,
            linkedin_post_status,
            twitter_post_status,
            some_configured
          ],
          company_profile_id
        )

      case res do
        {:ok, _} ->
          :ok

        error ->
          ErrorHandler.capture_exception(error)
          :ok
      end
    end)
  end

  # Note: Media stats caching functions have been moved to Gaia.MediaStatsCache module

  @doc """
  Delegate to MediaStatsCache for backward compatibility.
  """
  def calculate_and_cache_media_stats(%Media{} = media) do
    Gaia.Interactions.MediaStatsCache.calculate_and_cache_stats(media)
  end
end
