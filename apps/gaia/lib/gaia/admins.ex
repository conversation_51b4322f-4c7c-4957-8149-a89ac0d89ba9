defmodule Gaia.Admins do
  @moduledoc """
  The Admins context.
  """

  import Ecto.Query, warn: false

  alias G<PERSON>.Admins.AgentQuestion
  alias Gaia.Admins.User
  alias Gaia.Admins.UserSimulateToken
  alias Gaia.Admins.UserToken
  alias Gaia.Repo

  ## Database getters

  @doc """
  Gets a user by email.

  ## Examples

      iex> get_user_by_email("<EMAIL>")
      %User{}

      iex> get_user_by_email("<EMAIL>")
      nil

  """
  def get_user_by_email(email) when is_binary(email) do
    Repo.get_by(User, email: email)
  end

  @doc """
  Gets a user by email and password.

  ## Examples

      iex> get_user_by_email_and_password("<EMAIL>", "correct_password")
      %User{}

      iex> get_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_user_by_email_and_password(email, password) when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user(id), do: Repo.get(User, id)
  def get_user!(id), do: Repo.get!(User, id)

  def get_all_admins_users do
    User
    |> order_by([u], u.email)
    |> Repo.all()
  end

  def update_user_username(user, username) do
    user
    |> User.username_changeset(%{username: username})
    |> Repo.update()
  end

  ## User registration

  @doc """
  Registers an user.

  ## Examples

      iex> register_user(%{field: value})
      {:ok, %User{}}

      iex> register_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def register_user(attrs) do
    %User{}
    |> User.registration_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_user_registration(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_registration(%User{} = user, attrs \\ %{}) do
    User.registration_changeset(user, attrs, hash_password: false)
  end

  ## Settings

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user email.

  ## Examples

      iex> change_user_email(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_email(user, attrs \\ %{}) do
    User.email_changeset(user, attrs)
  end

  @doc """
  Emulates that the email will change without actually changing
  it in the database.

  ## Examples

      iex> apply_user_email(user, "valid password", %{email: ...})
      {:ok, %User{}}

      iex> apply_user_email(user, "invalid password", %{email: ...})
      {:error, %Ecto.Changeset{}}

  """
  def apply_user_email(user, password, attrs) do
    user
    |> User.email_changeset(attrs)
    |> User.validate_current_password(password)
    |> Ecto.Changeset.apply_action(:update)
  end

  @doc """
  Updates the user email using the given token.

  If the token matches, the user email is updated and the token is deleted.
  The confirmed_at date is also updated to the current time.
  """
  def update_user_email(user, token) do
    context = "change:#{user.email}"

    with {:ok, query} <- UserToken.verify_change_email_token_query(token, context),
         %UserToken{sent_to: email} <- Repo.one(query),
         {:ok, _} <- Repo.transaction(user_email_multi(user, email, context)) do
      :ok
    else
      _ -> :error
    end
  end

  defp user_email_multi(user, email, context) do
    changeset =
      user
      |> User.email_changeset(%{email: email})
      |> User.confirm_changeset()

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(
      :tokens,
      UserToken.user_and_contexts_query(user, [context])
    )
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user password.

  ## Examples

      iex> change_user_password(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_password(user, attrs \\ %{}) do
    User.password_changeset(user, attrs, hash_password: false)
  end

  @doc """
  Updates the user password.

  ## Examples

      iex> update_user_password(user, "valid password", %{password: ...})
      {:ok, %User{}}

      iex> update_user_password(user, "invalid password", %{password: ...})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_password(user, password, attrs) do
    changeset =
      user
      |> User.password_changeset(attrs)
      |> User.validate_current_password(password)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(
      :tokens,
      UserToken.user_and_contexts_query(user, :all)
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  @doc """
  Gets the user with the given signed token.
  """
  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)

    query
    |> Repo.one()
    |> Repo.preload(:roles)
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    Repo.delete_all(UserToken.token_and_context_query(token, "session"))
    :ok
  end

  ## Confirmation

  @doc """
  Delivers the confirmation email instructions to the given user.

  ## Examples

      iex> deliver_user_confirmation_instructions(user, &Routes.user_confirmation_url(conn, :edit, &1))
      {:ok, %{to: ..., body: ...}}

      iex> deliver_user_confirmation_instructions(confirmed_user, &Routes.user_confirmation_url(conn, :edit, &1))
      {:error, :already_confirmed}

  """
  def deliver_user_confirmation_instructions(%User{} = user, confirmation_url_fun)
      when is_function(confirmation_url_fun, 1) do
    if user.confirmed_at do
      {:error, :already_confirmed}
    else
      {_encoded_token, user_token} = UserToken.build_email_token(user, "confirm")
      Repo.insert!(user_token)
    end
  end

  @doc """
  Confirms a user by the given token.

  If the token matches, the user account is marked as confirmed
  and the token is deleted.
  """
  def confirm_user(token) do
    with {:ok, query} <- UserToken.verify_email_token_query(token, "confirm"),
         %User{} = user <- Repo.one(query),
         {:ok, %{user: user}} <-
           Repo.transaction(confirm_user_multi(user)) do
      {:ok, user}
    else
      _ -> :error
    end
  end

  defp confirm_user_multi(user) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, User.confirm_changeset(user))
    |> Ecto.Multi.delete_all(
      :tokens,
      UserToken.user_and_contexts_query(user, ["confirm"])
    )
  end

  ## Reset password

  @doc """
  Delivers the reset password email to the given user.

  ## Examples

      iex> deliver_user_reset_password_instructions(user, &Routes.user_reset_password_url(conn, :edit, &1))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_user_reset_password_instructions(%User{} = user, reset_password_url_fun)
      when is_function(reset_password_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "reset_password")
    Repo.insert!(user_token)

    Gaia.Notifications.Email.deliver(
      EmailTransactional.Operations,
      :password_reset_instructions,
      [user, encoded_token]
    )
  end

  @doc """
  Gets the user by reset password token.

  ## Examples

      iex> get_user_by_reset_password_token("validtoken")
      %User{}

      iex> get_user_by_reset_password_token("invalidtoken")
      nil

  """
  def get_user_by_reset_password_token(token) do
    with {:ok, query} <- UserToken.verify_email_token_query(token, "reset_password"),
         %User{} = user <- Repo.one(query) do
      user
    else
      _ -> nil
    end
  end

  @doc """
  Resets the user password.

  ## Examples

      iex> reset_user_password(user, %{password: "new long password", password_confirmation: "new long password"})
      {:ok, %User{}}

      iex> reset_user_password(user, %{password: "valid", password_confirmation: "not the same"})
      {:error, %Ecto.Changeset{}}

  """
  def reset_user_password(user, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, User.password_changeset(user, attrs))
    |> Ecto.Multi.delete_all(
      :tokens,
      UserToken.user_and_contexts_query(user, :all)
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  @doc """
  Returns the list of admins_user_simulate_tokens.

  ## Examples

      iex> list_admins_user_simulate_tokens()
      [%UserSimulateToken{}, ...]

  """
  def list_admins_user_simulate_tokens do
    Repo.all(UserSimulateToken)
  end

  @doc """
  Gets a single user_simulate_token.

  Raises `Ecto.NoResultsError` if the User simulate token does not exist.

  ## Examples

      iex> get_user_simulate_token!(123)
      %UserSimulateToken{}

      iex> get_user_simulate_token!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user_simulate_token!(id), do: Repo.get!(UserSimulateToken, id)

  @doc """
    Create simulate token and return the encode token
  """
  def create_simulate_encode_token(admin_user_id, company_user_id, company_profile_id, reason) do
    with {simulate_token, changeset} <-
           UserSimulateToken.build_simulate_token(admin_user_id, company_user_id),
         {:ok, _} <- Repo.insert(changeset),
         {:ok, _} <-
           Gaia.Tracking.create_admin_simulate(%{
             admin_user_id: admin_user_id,
             company_user_id: company_user_id,
             company_profile_id: company_profile_id,
             reason: reason
           }) do
      simulate_token
    else
      _ -> :error
    end
  end

  def get_admin_user_and_company_user_by_simulate_token(token) do
    invalidate_validty_in_days = UserSimulateToken.get_token_validity_in_days()
    decoded_token = UserSimulateToken.decode_simulate_token(token)

    UserSimulateToken
    |> where(
      [token],
      token.inserted_at > ago(^invalidate_validty_in_days, "day") and
        token.token == ^decoded_token
    )
    |> preload([token], [:admin_user, :company_user])
    |> Repo.one()
  end

  @doc """
  Updates a user_simulate_token.

  ## Examples

      iex> update_user_simulate_token(user_simulate_token, %{field: new_value})
      {:ok, %UserSimulateToken{}}

      iex> update_user_simulate_token(user_simulate_token, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_simulate_token(%UserSimulateToken{} = user_simulate_token, attrs) do
    user_simulate_token
    |> UserSimulateToken.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a user_simulate_token.

  ## Examples

      iex> delete_user_simulate_token(user_simulate_token)
      {:ok, %UserSimulateToken{}}

      iex> delete_user_simulate_token(user_simulate_token)
      {:error, %Ecto.Changeset{}}

  """
  def delete_user_simulate_token(%UserSimulateToken{} = user_simulate_token) do
    Repo.delete(user_simulate_token)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user_simulate_token changes.

  ## Examples

      iex> change_user_simulate_token(user_simulate_token)
      %Ecto.Changeset{data: %UserSimulateToken{}}

  """
  def change_user_simulate_token(%UserSimulateToken{} = user_simulate_token, attrs \\ %{}) do
    UserSimulateToken.changeset(user_simulate_token, attrs)
  end

  ## Agent Questions

  @doc """
  Returns the list of agent questions.

  ## Examples

      iex> list_agent_questions()
      [%AgentQuestion{}, ...]

  """
  def list_agent_questions do
    AgentQuestion
    |> order_by([q], desc: q.inserted_at)
    |> preload(:admin_user)
    |> Repo.all()
  end

  @doc """
  Returns the list of agent questions for a specific admin user.

  ## Examples

      iex> list_agent_questions_by_user(123)
      [%AgentQuestion{}, ...]

  """
  def list_agent_questions_by_user(admin_user_id) do
    AgentQuestion
    |> where([q], q.admin_user_id == ^admin_user_id)
    |> order_by([q], desc: q.inserted_at)
    |> preload(:admin_user)
    |> Repo.all()
  end

  @doc """
  Gets a single agent question.

  Raises `Ecto.NoResultsError` if the Agent question does not exist.

  ## Examples

      iex> get_agent_question!(123)
      %AgentQuestion{}

      iex> get_agent_question!(456)
      ** (Ecto.NoResultsError)

  """
  def get_agent_question!(id), do: Repo.get!(AgentQuestion, id)

  @doc """
  Creates an agent question.

  ## Examples

      iex> create_agent_question(%{field: value})
      {:ok, %AgentQuestion{}}

      iex> create_agent_question(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_agent_question(attrs \\ %{}) do
    %AgentQuestion{}
    |> AgentQuestion.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an agent question.

  ## Examples

      iex> update_agent_question(agent_question, %{field: new_value})
      {:ok, %AgentQuestion{}}

      iex> update_agent_question(agent_question, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_agent_question(%AgentQuestion{} = agent_question, attrs) do
    agent_question
    |> AgentQuestion.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes an agent question.

  ## Examples

      iex> delete_agent_question(agent_question)
      {:ok, %AgentQuestion{}}

      iex> delete_agent_question(agent_question)
      {:error, %Ecto.Changeset{}}

  """
  def delete_agent_question(%AgentQuestion{} = agent_question) do
    Repo.delete(agent_question)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking agent question changes.

  ## Examples

      iex> change_agent_question(agent_question)
      %Ecto.Changeset{data: %AgentQuestion{}}

  """
  def change_agent_question(%AgentQuestion{} = agent_question, attrs \\ %{}) do
    AgentQuestion.changeset(agent_question, attrs)
  end
end
