defmodule Gaia.Comms do
  @moduledoc """
  The Comms context.
  """
  use Helper.Pipe
  use Helper.Pipe

  import Ecto.Query, warn: false

  alias Gaia.Comms
  alias Gaia.Comms.BaseEmailTemplate
  alias Gaia.Comms.ContactGlobalUnsubscribe
  alias Gaia.Comms.ContactSuppression
  alias Gaia.Comms.ContactUnsubscribe
  alias Gaia.Comms.CustomEmail
  alias Gaia.Comms.Email
  alias Gaia.Comms.EmailRecipient
  alias Gaia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Flows
  alias Gaia.Flows.DistributionSettingsEmail
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.MediaAnnouncement
  alias Gaia.Interactions.MediaUpdate
  alias Gaia.Markets.Ticker
  alias Gaia.Repo

  def batch_get_shareholder_email_recipients(
        %{email_id: email_id, company_profile_id: company_profile_id},
        shareholder_ids
      ) do
    EmailRecipient
    |> join(:inner, [r], email in assoc(r, :email))
    |> where([r, _], r.shareholder_id in ^shareholder_ids)
    |> where([_, email], email.id == ^email_id)
    |> where([_, email], not email.invalidated)
    |> where([_, email], email.company_profile_id == ^company_profile_id)
    |> where([_, email], not email.invalidated)
    |> Repo.all()
  end

  def batch_get_contact_email_recipients(%{email_id: email_id, company_profile_id: company_profile_id}, contact_ids) do
    EmailRecipient
    |> join(:inner, [recipient], email in assoc(recipient, :email))
    |> where([recipient, _], recipient.contact_id in ^contact_ids)
    |> where([_, email], email.id == ^email_id)
    |> where([_, email], not email.invalidated)
    |> where([_, email], email.company_profile_id == ^company_profile_id)
    |> where([_, email], not email.invalidated)
    |> Repo.all()
  end

  @doc """
  Returns a query

  This query contains all email_recipient to be deleted
  They should have matching contact_id with the input
  They should have not been sent

  Use case:
  Need to remove contact as recipient when contact has unsubscribed from general scope
  Do not need to remove from already sent email

  ## Examples

      iex> delete_draft_email_recipient_by_contact_id_query(contact_id)
      %Ecto.Query{}

  """
  def delete_draft_email_recipient_by_contact_id_query(contact_id) do
    EmailRecipient
    |> where([er], er.contact_id == ^contact_id)
    |> where([er], is_nil(er.tracking_email_id) and is_nil(er.sent_at))
  end

  @doc """
  Returns a query

  This query contains all email_recipient to be deleted
  They should have matching email with the input
  They should have matching company_profile_id with the input
  They should have not been sent

  Use case:
  Need to remove contact as email recipient when contact has been suppressed
  Do not need to remove from already sent email

  ## Examples

      iex> delete_draft_email_recipient_by_email_query(email, company_profile_id)
      %Ecto.Query{}

  """
  def delete_draft_email_recipient_by_email_query(email, company_profile_id) do
    EmailRecipient
    |> join(:inner, [er], assoc(er, :contact))
    |> where([er], is_nil(er.tracking_email_id) and is_nil(er.sent_at))
    |> where([_, c], fragment("trim(lower(?)) = trim(lower(?))", c.email, ^email))
    |> where([_, c], c.company_profile_id == ^company_profile_id)
  end

  @doc """
  Returns the list of comms_custom_emails.

  ## Examples

      iex> list_comms_custom_emails()
      [%CustomEmail{}, ...]

  """
  def list_comms_custom_emails do
    Repo.all(CustomEmail)
  end

  @doc """
  Gets a single custom_email.

  Raises `Ecto.NoResultsError` if the Custom email does not exist.

  ## Examples

      iex> get_custom_email!(123)
      %CustomEmail{}

      iex> get_custom_email!(456)
      ** (Ecto.NoResultsError)

  """
  def get_custom_email!(id), do: Repo.get!(CustomEmail, id)
  def get_custom_email(id), do: Repo.get(CustomEmail, id)
  def get_custom_email_by(attrs), do: Repo.get_by(CustomEmail, attrs)

  @doc """
  Creates a custom_email.

  ## Examples

      iex> create_custom_email(%{field: value})
      {:ok, %CustomEmail{}}

      iex> create_custom_email(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_custom_email(attrs \\ %{}) do
    %CustomEmail{}
    |> CustomEmail.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a custom_email.

  ## Examples

      iex> update_custom_email(custom_email, %{field: new_value})
      {:ok, %CustomEmail{}}

      iex> update_custom_email(custom_email, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_custom_email(%CustomEmail{} = custom_email, attrs) do
    custom_email
    |> CustomEmail.changeset(attrs)
    |> Repo.update()
  end

  def upsert_custom_email_by_type(%{type: type} = attrs, company_profile_id) do
    %{company_profile_id: company_profile_id, type: type}
    |> get_custom_email_by()
    |> case do
      nil ->
        attrs
        |> Map.put(:company_profile_id, company_profile_id)
        |> create_custom_email()

      existing_custom_email ->
        update_custom_email(existing_custom_email, attrs)
    end
  end

  @doc """
  Deletes a custom_email.

  ## Examples

      iex> delete_custom_email(custom_email)
      {:ok, %CustomEmail{}}

      iex> delete_custom_email(custom_email)
      {:error, %Ecto.Changeset{}}

  """
  def delete_custom_email(%CustomEmail{} = custom_email) do
    Repo.delete(custom_email)
  end

  def delete_all_custom_emails_by(attrs \\ []) do
    CustomEmail
    |> where(^attrs)
    |> Repo.delete_all()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking custom_email changes.

  ## Examples

      iex> change_custom_email(custom_email)
      %Ecto.Changeset{data: %CustomEmail{}}

  """
  def change_custom_email(%CustomEmail{} = custom_email, attrs \\ %{}) do
    CustomEmail.changeset(custom_email, attrs)
  end

  def get_custom_emails_by_company_profile_id(company_profile_id) do
    marketing_email =
      CustomEmail
      |> where([mce], mce.company_profile_id == ^company_profile_id and mce.type == :marketing)
      |> Repo.one()

    transactional_email =
      CustomEmail
      |> where([ce], ce.company_profile_id == ^company_profile_id and ce.type == :transactional)
      |> Repo.one()

    %{
      marketing_email: marketing_email,
      transactional_email: transactional_email,
      is_transactional_email_same_as_marketing_email:
        check_is_transactional_email_same_as_marketing_email(%{
          marketing_email: marketing_email,
          transactional_email: transactional_email
        })
    }
  end

  def check_is_transactional_email_same_as_marketing_email(%{
        marketing_email: marketing_email,
        transactional_email: transactional_email
      })
      when not is_nil(marketing_email) and not is_nil(transactional_email) do
    Enum.all?(
      [:send_from_email, :send_from_name, :reply_to_email],
      &(Map.get(marketing_email, &1) == Map.get(transactional_email, &1))
    )
  end

  def check_is_transactional_email_same_as_marketing_email(_), do: false

  def get_custom_email_by_type(custom_emails, type) do
    custom_emails
    |> Enum.find(&(&1.type === type))
    |> case do
      nil ->
        if type != :marketing do
          # Marketing email is mandatory for setting up custom emails,
          # so use custom_marketing_email as fallback value.
          get_custom_email_by_type(custom_emails, :marketing)
        else
          # Throw error if not setted up
          raise "Custom email is not found"
        end

      %Gaia.Comms.CustomEmail{} = custom_email ->
        custom_email
    end
  end

  @doc """
  Returns the list of comms_contact_unsubscribes.

  ## Examples

      iex> list_comms_contact_unsubscribes()
      [%ContactUnsubscribe{}, ...]

  """
  def list_comms_contact_unsubscribes do
    Repo.all(ContactUnsubscribe)
  end

  @doc """
  Gets a single contact_unsubscribe.

  Raises `Ecto.NoResultsError` if the Contact unsubscribe does not exist.

  ## Examples

      iex> get_contact_unsubscribe!(123)
      %ContactUnsubscribe{}

      iex> get_contact_unsubscribe!(456)
      ** (Ecto.NoResultsError)
  """
  def get_contact_unsubscribe!(id), do: Repo.get!(ContactUnsubscribe, id)

  def get_contact_unsubscribe_by(attrs), do: Repo.get_by(ContactUnsubscribe, attrs)

  @doc """
  Creates a contact_unsubscribe.

  ## Examples

      iex> create_contact_unsubscribe(%{field: value})
      {:ok, %ContactUnsubscribe{}}

      iex> create_contact_unsubscribe(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_contact_unsubscribe(attrs \\ %{}) do
    %ContactUnsubscribe{}
    |> ContactUnsubscribe.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a contact_unsubscribe.

  ## Examples

      iex> update_contact_unsubscribe(contact_unsubscribe, %{field: new_value})
      {:ok, %ContactUnsubscribe{}}

      iex> update_contact_unsubscribe(contact_unsubscribe, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_contact_unsubscribe(%ContactUnsubscribe{} = contact_unsubscribe, attrs) do
    contact_unsubscribe
    |> ContactUnsubscribe.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a contact_unsubscribe.

  ## Examples

      iex> delete_contact_unsubscribe(contact_unsubscribe)
      {:ok, %ContactUnsubscribe{}}

      iex> delete_contact_unsubscribe(contact_unsubscribe)
      {:error, %Ecto.Changeset{}}

  """
  def delete_contact_unsubscribe(%ContactUnsubscribe{} = contact_unsubscribe) do
    Repo.delete(contact_unsubscribe)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking contact_unsubscribe changes.

  ## Examples

      iex> change_contact_unsubscribe(contact_unsubscribe)
      %Ecto.Changeset{data: %ContactUnsubscribe{}}

  """
  def change_contact_unsubscribe(%ContactUnsubscribe{} = contact_unsubscribe, attrs \\ %{}) do
    ContactUnsubscribe.changeset(contact_unsubscribe, attrs)
  end

  @doc """
  Delete all contact_unsubscribe belong to a contact
  """
  def delete_contact_unsubscribes_by_contact(%Contact{id: contact_id}) do
    ContactUnsubscribe
    |> where(contact_id: ^contact_id)
    |> Repo.delete_all()
  end

  # Subscribed if not globally unsubscribed and not unsubscribed to the scope
  def is_contact_subscribed?(contact_id, scope) when is_binary(contact_id) or is_integer(contact_id) do
    contact_id
    |> Contacts.get_contact()
    |> is_contact_subscribed?(scope)
  end

  def is_contact_subscribed?(
        %Contact{comms_unsubscribes: comms_unsubscribes, global_unsubscribe: global_unsubscribe} = contact,
        scope
      )
      when is_struct(comms_unsubscribes, Ecto.Association.NotLoaded) or
             is_struct(global_unsubscribe, Ecto.Association.NotLoaded) do
    contact
    |> Repo.preload([:comms_unsubscribes, :global_unsubscribe])
    |> is_contact_subscribed?(scope)
  end

  def is_contact_subscribed?(%Contact{comms_unsubscribes: comms_unsubscribes, global_unsubscribe: nil}, scope)
      when is_list(comms_unsubscribes) do
    comms_unsubscribes
    |> Enum.find(&("#{&1.scope}" == "#{scope}"))
    |> case do
      nil -> true
      _ -> false
    end
  end

  def is_contact_subscribed?(_contact, _scope), do: false

  # Subscribed if not globally unsubscribed and not unsubscribed to the scope
  def is_investor_subscribed(%{investor_user: %Gaia.Investors.User{contact: %struct{} = contact} = user, scope: scope}) do
    struct
    |> case do
      Ecto.Association.NotLoaded -> user |> Repo.preload(:contact) |> Map.get(:contact)
      Contact -> contact
    end
    |> is_contact_subscribed?(scope)
  end

  def is_investor_subscribed(_), do: false

  @doc """
  Returns the list of base_email_templates.

  ## Examples

      iex> list_base_email_templates()
      [%BaseEmailTemplate{}, ...]

  """
  def list_base_email_templates do
    Repo.all(BaseEmailTemplate)
  end

  @doc """
  Gets a single base_email_template.

  Raises `Ecto.NoResultsError` if the Base email template does not exist.

  ## Examples

      iex> get_base_email_template!(123)
      %BaseEmailTemplate{}

      iex> get_base_email_template!(456)
      ** (Ecto.NoResultsError)

  """
  def get_base_email_template(id), do: Repo.get(BaseEmailTemplate, id)
  def get_base_email_template!(id), do: Repo.get!(BaseEmailTemplate, id)
  def get_base_email_template_by(attrs), do: Repo.get_by(BaseEmailTemplate, attrs)

  @doc """
  Creates a base_email_template.

  ## Examples

      iex> create_base_email_template(%{field: value})
      {:ok, %BaseEmailTemplate{}}

      iex> create_base_email_template(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_base_email_template(attrs \\ %{}) do
    %BaseEmailTemplate{}
    |> BaseEmailTemplate.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a base_email_template.

  ## Examples

      iex> update_base_email_template(base_email_template, %{field: new_value})
      {:ok, %BaseEmailTemplate{}}

      iex> update_base_email_template(base_email_template, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_base_email_template(%BaseEmailTemplate{} = base_email_template, attrs) do
    base_email_template
    |> BaseEmailTemplate.changeset(attrs)
    |> Repo.update()
  end

  def upsert_base_email_template_by_company_profile_id(attrs, company_profile_id) do
    template_type = Map.get(attrs, :template_type, :custom_campaign)

    template =
      get_base_email_template_by(%{
        company_profile_id: company_profile_id,
        template_type: template_type
      })

    case template do
      nil ->
        attrs |> Map.put(:company_profile_id, company_profile_id) |> create_base_email_template()

      existing_base_email_template ->
        update_base_email_template(existing_base_email_template, attrs)
    end
  end

  def maybe_update_flows_distributions_settings_emails(attrs, :automated_distribution),
    do: exec_update_flows_distributions_settings_emails(attrs)

  def maybe_update_flows_distributions_settings_emails(attrs, :new_shareholder_welcome),
    do: exec_update_flows_distributions_settings_emails(attrs, :new_shareholder_welcome)

  def maybe_update_flows_distributions_settings_emails(_, _), do: :ok

  defp exec_update_flows_distributions_settings_emails(%{company_profile_id: company_profile_id} = attrs) do
    company_profile_id
    |> Flows.get_distribution_settings_email_by_company_profile_id()
    |> Enum.each(
      &case &1 do
        %DistributionSettingsEmail{} = existing_distribution_settings_email ->
          Flows.update_distribution_settings_email(existing_distribution_settings_email, attrs)

        nil ->
          {:ok, nil}
      end
    )
  end

  defp exec_update_flows_distributions_settings_emails(
         %{company_profile_id: company_profile_id} = attrs,
         :new_shareholder_welcome
       ) do
    company_profile_id
    |> Flows.get_distribution_settings_email_by_company_profile_id(:new_shareholder_welcome)
    |> Enum.each(
      &case &1 do
        %DistributionSettingsEmail{} = existing_distribution_settings_email ->
          Flows.update_distribution_settings_email(existing_distribution_settings_email, attrs)

        nil ->
          {:ok, nil}
      end
    )
  end

  @doc """
  Deletes a base_email_template.

  ## Examples

      iex> delete_base_email_template(base_email_template)
      {:ok, %BaseEmailTemplate{}}

      iex> delete_base_email_template(base_email_template)
      {:error, %Ecto.Changeset{}}

  """
  def delete_base_email_template(%BaseEmailTemplate{} = base_email_template) do
    Repo.delete(base_email_template)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking base_email_template changes.

  ## Examples

      iex> change_base_email_template(base_email_template)
      %Ecto.Changeset{data: %BaseEmailTemplate{}}

  """
  def change_base_email_template(%BaseEmailTemplate{} = base_email_template, attrs \\ %{}) do
    BaseEmailTemplate.changeset(base_email_template, attrs)
  end

  def get_or_build_base_email_template_by_template_type(
        %Gaia.Companies.Profile{id: company_profile_id} = profile,
        template_type
      ) do
    %{company_profile_id: company_profile_id, template_type: template_type}
    |> Comms.get_base_email_template_by()
    |> case do
      nil ->
        get_company_prefilled_email_template_html_and_json(
          %BaseEmailTemplate{
            email_html: get_default_email_template_html(template_type),
            email_json: get_default_email_template_json(template_type),
            template_type: template_type,
            company_profile_id: company_profile_id,
            inserted_at: NaiveDateTime.utc_now(:second),
            updated_at: NaiveDateTime.utc_now(:second)
          },
          profile
        )

      %BaseEmailTemplate{} = base_email_template ->
        get_company_prefilled_email_template_html_and_json(base_email_template, profile)
    end
  end

  defp get_company_prefilled_email_template_html_and_json(
         %BaseEmailTemplate{email_html: _email_html, email_json: _email_json, template_type: template_type} =
           base_email_template,
         %Gaia.Companies.Profile{} = profile
       ) do
    with {:ok, merge_tag_params} <-
           get_company_email_template_merge_tag_params(profile, template_type),
         %BaseEmailTemplate{} = updated_base_email_template <-
           replace_company_email_template_merge_tag_params(base_email_template, merge_tag_params) do
      updated_base_email_template
    else
      _error -> nil
    end
  end

  def get_company_email_template_merge_tag_params(%Gaia.Companies.Profile{} = profile, distribution_type) do
    case Repo.preload(profile, [:custom_domain, :investor_hub, :ticker]) do
      %Gaia.Companies.Profile{
        custom_domain: company_custom_domain,
        investor_hub: investor_hub,
        ticker: %Gaia.Markets.Ticker{} = ticker,
        name: company_name,
        trading_name: trading_name
      } ->
        company_base_url = Gaia.Companies.get_company_base_url(company_custom_domain, ticker)

        {:ok,
         get_merge_tag_params(
           %{
             investor_hub_url: company_base_url,
             company_logo: Gaia.Companies.get_profile_logo_url(profile),
             street_address: get_street_address(profile),
             city_state_country_postcode: get_city_state_country_postcode(profile),
             linkedin: get_social_link(investor_hub, :linkedin, company_base_url),
             facebook: get_social_link(investor_hub, :facebook, company_base_url),
             twitter: get_social_link(investor_hub, :twitter, company_base_url),
             company_name: company_name,
             trading_name: trading_name
           },
           distribution_type
         )}

      _ ->
        {:error, "Can not create template due to investor hub settings"}
    end
  end

  defp replace_company_email_template_merge_tag_params(
         %BaseEmailTemplate{email_html: email_html, email_json: email_json, template_type: :custom_campaign} =
           base_email_template,
         merge_tag_params
       ) do
    template_json =
      Gaia.Helpers.Comms.CustomCampaignMergeTags.replace_merge_tag(merge_tag_params, email_json)

    template_html =
      Gaia.Helpers.Comms.CustomCampaignMergeTags.replace_merge_tag(merge_tag_params, email_html)

    base_email_template
    |> Map.put(:email_json, template_json)
    |> Map.put(:email_html, template_html)
  end

  defp replace_company_email_template_merge_tag_params(
         %BaseEmailTemplate{email_html: email_html, email_json: email_json, template_type: :automated_distribution} =
           base_email_template,
         merge_tag_params
       ) do
    template_json =
      Gaia.Helpers.Comms.AutomatedDistributionMergeTags.replace_merge_tag(
        merge_tag_params,
        email_json
      )

    template_html =
      Gaia.Helpers.Comms.AutomatedDistributionMergeTags.replace_merge_tag(
        merge_tag_params,
        email_html
      )

    base_email_template
    |> Map.put(:email_json, template_json)
    |> Map.put(:email_html, template_html)
  end

  defp replace_company_email_template_merge_tag_params(
         %BaseEmailTemplate{email_html: email_html, email_json: email_json, template_type: :manual_distribution} =
           base_email_template,
         merge_tag_params
       ) do
    template_json =
      Gaia.Helpers.Comms.ManualDistributionMergeTags.replace_merge_tag(
        merge_tag_params,
        email_json
      )

    template_html =
      Gaia.Helpers.Comms.ManualDistributionMergeTags.replace_merge_tag(
        merge_tag_params,
        email_html
      )

    base_email_template
    |> Map.put(:email_json, template_json)
    |> Map.put(:email_html, template_html)
  end

  defp replace_company_email_template_merge_tag_params(
         %BaseEmailTemplate{email_html: email_html, email_json: email_json, template_type: :new_shareholder_welcome} =
           base_email_template,
         merge_tag_params
       ) do
    template_json =
      Gaia.Helpers.Comms.NewShareholderWelcomeMergeTags.replace_merge_tag(
        merge_tag_params,
        email_json
      )

    template_html =
      Gaia.Helpers.Comms.NewShareholderWelcomeMergeTags.replace_merge_tag(
        merge_tag_params,
        email_html
      )

    base_email_template
    |> Map.put(:email_json, template_json)
    |> Map.put(:email_html, template_html)
  end

  def get_social_link(%Gaia.Companies.InvestorHub{} = investor_hub, type, company_base_url) do
    Map.get(investor_hub, type, company_base_url)
  end

  def get_social_link(_investor_hub, _type, company_base_url) do
    company_base_url
  end

  def get_street_address(%Gaia.Companies.Profile{address_line1: address_line1, address_line2: address_line2}) do
    case {address_line1, address_line2} do
      {nil, nil} -> ""
      {nil, _} -> address_line2
      {_, nil} -> address_line1
      {_, _} -> "#{address_line1} #{address_line2}"
    end
  end

  def get_city_state_country_postcode(%Gaia.Companies.Profile{
        city: city,
        country: country,
        postal_code: postcode,
        state: state
      }) do
    [city, state, country, postcode]
    |> Enum.filter(&(not is_nil(&1)))
    |> Enum.map(&String.trim/1)
    |> Enum.filter(&(&1 != ""))
    |> Enum.join(", ")
  end

  defp get_merge_tag_params(args, :custom_campaign) do
    args
    |> Enum.filter(fn {_, v} -> v end)
    |> Map.new()
    |> struct(%Gaia.Helpers.Comms.CustomCampaignMergeTags{}, __)
  end

  defp get_merge_tag_params(args, :automated_distribution) do
    args
    |> Enum.filter(fn {_, v} -> v end)
    |> Map.new()
    |> struct(%Gaia.Helpers.Comms.AutomatedDistributionMergeTags{}, __)
  end

  defp get_merge_tag_params(args, :manual_distribution) do
    args
    |> Enum.filter(fn {_, v} -> v end)
    |> Map.new()
    |> struct(%Gaia.Helpers.Comms.ManualDistributionMergeTags{}, __)
  end

  defp get_merge_tag_params(args, :new_shareholder_welcome) do
    args
    |> Enum.filter(fn {_, v} -> v end)
    |> Map.new()
    |> struct(%Gaia.Helpers.Comms.NewShareholderWelcomeMergeTags{}, __)
  end

  def get_default_email_template_html(:custom_campaign) do
    File.read!(Application.app_dir(:athena, "priv/data/custom_campaign_base_email_template.html"))
  end

  def get_default_email_template_html(:automated_distribution) do
    File.read!(Application.app_dir(:athena, "priv/data/automated_distribution_base_email_template.html"))
  end

  def get_default_email_template_html(:manual_distribution) do
    File.read!(Application.app_dir(:athena, "priv/data/manual_distribution_base_email_template.html"))
  end

  def get_default_email_template_html(:new_shareholder_welcome) do
    File.read!(Application.app_dir(:athena, "priv/data/new_shareholder_welcome_base_email_template.html"))
  end

  def get_default_email_template_json(:custom_campaign) do
    File.read!(Application.app_dir(:athena, "priv/data/custom_campaign_base_email_template.json"))
  end

  def get_default_email_template_json(:automated_distribution) do
    File.read!(Application.app_dir(:athena, "priv/data/automated_distribution_base_email_template.json"))
  end

  def get_default_email_template_json(:manual_distribution) do
    File.read!(Application.app_dir(:athena, "priv/data/manual_distribution_base_email_template.json"))
  end

  def get_default_email_template_json(:new_shareholder_welcome) do
    File.read!(Application.app_dir(:athena, "priv/data/new_shareholder_welcome_base_email_template.json"))
  end

  @doc """
  Returns the list of comms_contact_suppressions.

  ## Examples

      iex> list_comms_contact_suppressions()
      [%ContactSuppression{}, ...]

  """
  def list_comms_contact_suppressions do
    Repo.all(ContactSuppression)
  end

  @doc """
  Gets a single contact_suppression.

  ## Examples

      iex> get_contact_suppression(123)
      %ContactSuppression{}

      iex> get_contact_suppression(456)
      ** nil

  """
  def get_contact_suppression(id), do: Repo.get(ContactSuppression, id)

  @doc """
    Checke if email is in suppressed list for specific company
    ## Examples
      iex > is_contact_suppressed?("<EMAIL>", 1)
      true

      iex > is_contact_suppressed?("<EMAIL>", 2)
      false
  """
  def is_contact_suppressed?(email, contact_id) do
    # All fake generated emails using {name}@email.com for demo accounts will
    # be seen as suppressed so we don't send to them and affect deliverability
    if String.match?(email, ~r/@email.com/),
      do: true,
      else:
        ContactSuppression
        |> where([c], c.contact_id == ^contact_id)
        |> Repo.exists?()
  end

  @doc """
    Filter Unsuppressed Emails for providing companys
    iex > filter_unsuppressed_emails(["<EMAIL>", "<EMAIL>"], 1)
    ["<EMAIL>"]
  """
  def filter_unsuppressed_emails(emails, company_profile_id) when is_list(emails) do
    ContactSuppression
    |> where([c], c.email in ^emails and c.company_profile_id == ^company_profile_id)
    |> select([c], c.email)
    |> Repo.all()
    |> Kernel.--(emails, __)
  end

  def filter_unsuppressed_emails(_, _), do: {:error, "Wrong Params"}

  @doc """
  Creates a contact_suppression.

  ## Examples

      iex> create_contact_suppression(%{field: value})
      {:ok, %ContactSuppression{}}

      iex> create_contact_suppression(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_contact_suppression(attrs \\ %{}) do
    %ContactSuppression{}
    |> ContactSuppression.changeset(attrs)
    |> Repo.insert(on_conflict: :nothing)
  end

  @doc """
  Updates a contact_suppression.

  ## Examples

      iex> update_contact_suppression(contact_suppression, %{field: new_value})
      {:ok, %ContactSuppression{}}

      iex> update_contact_suppression(contact_suppression, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_contact_suppression(%ContactSuppression{} = contact_suppression, attrs) do
    contact_suppression
    |> ContactSuppression.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a contact_suppression.

  ## Examples

      iex> delete_contact_suppression(contact_suppression)
      {:ok, %ContactSuppression{}}

      iex> delete_contact_suppression(contact_suppression)
      {:error, %Ecto.Changeset{}}

  """
  def delete_contact_suppression(%ContactSuppression{} = contact_suppression) do
    Repo.delete(contact_suppression)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking contact_suppression changes.

  ## Examples

      iex> change_contact_suppression(contact_suppression)
      %Ecto.Changeset{data: %ContactSuppression{}}

  """
  def change_contact_suppression(%ContactSuppression{} = contact_suppression, attrs \\ %{}) do
    ContactSuppression.changeset(contact_suppression, attrs)
  end

  @doc """
  Returns the list of comms_contact_global_unsubscribes.

  ## Examples

      iex> list_comms_contact_global_unsubscribes()
      [%ContactGlobalUnsubscribe{}, ...]

  """
  def list_comms_contact_global_unsubscribes do
    Repo.all(ContactGlobalUnsubscribe)
  end

  @doc """
  Gets a single contact_global_unsubscribe.

  Raises `Ecto.NoResultsError` if the Contact global unsubscribe does not exist.

  ## Examples

      iex> get_contact_global_unsubscribe!(123)
      %ContactGlobalUnsubscribe{}

      iex> get_contact_global_unsubscribe!(456)
      ** (Ecto.NoResultsError)

  """
  def get_contact_global_unsubscribe!(id), do: Repo.get!(ContactGlobalUnsubscribe, id)

  @doc """
  Gets a single contact_global_unsubscribe.

  Returns nil if the ContactGlobalUnsubscribe does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_contact_global_unsubscribe_by(%{key: value})
      %ContactGlobalUnsubscribe{}

      iex> get_contact_global_unsubscribe_by(%{key: value})
      nil

      iex> get_contact_global_unsubscribe_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_contact_global_unsubscribe_by(attrs), do: Repo.get_by(ContactGlobalUnsubscribe, attrs)

  @doc """
  Creates a contact_global_unsubscribe.

  ## Examples

      iex> create_contact_global_unsubscribe(%{field: value})
      {:ok, %ContactGlobalUnsubscribe{}}

      iex> create_contact_global_unsubscribe(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_contact_global_unsubscribe(attrs \\ %{}) do
    %ContactGlobalUnsubscribe{}
    |> ContactGlobalUnsubscribe.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a contact_global_unsubscribe.

  ## Examples

      iex> update_contact_global_unsubscribe(contact_global_unsubscribe, %{field: new_value})
      {:ok, %ContactGlobalUnsubscribe{}}

      iex> update_contact_global_unsubscribe(contact_global_unsubscribe, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_contact_global_unsubscribe(%ContactGlobalUnsubscribe{} = contact_global_unsubscribe, attrs) do
    contact_global_unsubscribe
    |> ContactGlobalUnsubscribe.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a contact_global_unsubscribe.

  ## Examples

      iex> delete_contact_global_unsubscribe(contact_global_unsubscribe)
      {:ok, %ContactGlobalUnsubscribe{}}

      iex> delete_contact_global_unsubscribe(contact_global_unsubscribe)
      {:error, %Ecto.Changeset{}}

  """
  def delete_contact_global_unsubscribe(%ContactGlobalUnsubscribe{} = contact_global_unsubscribe) do
    Repo.delete(contact_global_unsubscribe)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking contact_global_unsubscribe changes.

  ## Examples

      iex> change_contact_global_unsubscribe(contact_global_unsubscribe)
      %Ecto.Changeset{data: %ContactGlobalUnsubscribe{}}

  """
  def change_contact_global_unsubscribe(%ContactGlobalUnsubscribe{} = contact_global_unsubscribe, attrs \\ %{}) do
    ContactGlobalUnsubscribe.changeset(contact_global_unsubscribe, attrs)
  end

  # TODO: run this migration for all exisiting contacts who haven't globally unsubscribed from all notifications before release
  def add_new_notification_preference_settings_for_all_existing_contacts do
    Gaia.Contacts.Contact
    |> join(:left, [c], u in Gaia.Comms.ContactGlobalUnsubscribe, on: c.id == u.contact_id)
    |> where([c, u], not c.invalidated and is_nil(u.id))
    |> select([c, u], %{
      contact_id: c.id,
      company_profile_id: c.company_profile_id
    })
    |> Repo.all()
    |> Enum.chunk_every(4_000)
    |> Enum.map(&add_new_notification_preference_settings_for_existing_contacts(&1))
  end

  def add_new_notification_preference_settings_for_existing_contacts(contacts) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert_all(
      :activity_follow_unsubscribes,
      Gaia.Comms.ContactUnsubscribe,
      Enum.map(
        contacts,
        &%{
          contact_id: &1.contact_id,
          company_profile_id: &1.company_profile_id,
          scope: :activity_follow,
          inserted_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second),
          updated_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)
        }
      ),
      on_conflict: {:replace, [:updated_at]},
      conflict_target: [:contact_id, :scope]
    )
    |> Ecto.Multi.insert_all(
      :new_follower_unsubscribes,
      Gaia.Comms.ContactUnsubscribe,
      Enum.map(
        contacts,
        &%{
          contact_id: &1.contact_id,
          company_profile_id: &1.company_profile_id,
          scope: :new_follower,
          inserted_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second),
          updated_at: NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)
        }
      ),
      on_conflict: {:replace, [:updated_at]},
      conflict_target: [:contact_id, :scope]
    )
    |> Gaia.Repo.transaction(timeout: 600_000)
  end

  @doc """
  Returns the list of comms_emails.

  ## Examples

      iex> list_comms_emails()
      [%Email{}, ...]

  """
  def list_comms_emails do
    Repo.all(Email)
  end

  @doc """
  Gets a single email.

  Raises `Ecto.NoResultsError` if the Email does not exist.

  ## Examples

      iex> get_email!(123)
      %Email{}

      iex> get_email!(456)
      ** (Ecto.NoResultsError)

  """
  def get_email!(id), do: Repo.get!(Email, id)
  def get_email(id), do: Repo.get(Email, id)
  def get_email_by(args), do: Repo.get_by(Email, args)

  def count_total_recipient_events_by_email(id, :Unsubscribed) do
    unsubscribed_contact_ids =
      Email
      |> join(:inner, [email], assoc(email, :email_unsubscribed_contacts))
      |> where([email], email.id == ^id)
      |> distinct(true)
      |> select([_email, unsubscribe], unsubscribe.contact_id)
      |> Repo.all()

    globally_unsubscribed_contact_ids =
      Email
      |> join(:inner, [email], assoc(email, :email_globally_unsubscribed_contacts))
      |> where([email], email.id == ^id)
      |> distinct(true)
      |> select([_email, unsubscribe], unsubscribe.contact_id)
      |> Repo.all()

    unsubscribed_contact_ids
    |> Kernel.++(globally_unsubscribed_contact_ids)
    |> Enum.uniq()
    |> Enum.count()
  end

  def count_total_recipient_events_by_email(id, :Total) do
    Email
    |> join(:right, [email], email_recipient in assoc(email, :email_recipients))
    |> where([email, ...], email.id == ^id)
    |> Repo.aggregate(:count)
  end

  def count_total_recipient_events_by_email(id, type) do
    Email
    |> join(:right, [email], event in assoc(email, :recipients_tracking_events))
    |> where([email, event], email.id == ^id and event.event_type == ^type)
    |> select([email, event], count(fragment("DISTINCT ?", event.email_id)))
    |> Repo.one()
  end

  def emails_query_by_company_profile_id(args \\ %{filters: [], orders: []}, id) do
    args
    |> Map.put(:filters, [%{key: "company_profile_id", value: id} | args.filters])
    |> Enum.reduce(Email, fn
      {:orders, orders}, query ->
        emails_order_with(query, orders)

      {:filters, filters}, query ->
        emails_filter_with(query, filters)

      _, query ->
        query
    end)
  end

  defp emails_filter_with(query, filter) do
    Enum.reduce(filter, query, fn
      %{key: "company_profile_id", value: value}, query ->
        from(q in query, where: q.company_profile_id == ^value)

      %{key: "search", value: value}, query ->
        from(q in query,
          where: ilike(q.campaign_name, ^"%#{value}%")
        )

      %{key: "type", value: "all_except_welcome"}, query ->
        from(q in query, where: not q.is_welcome_email)

      %{key: "type", value: "general"}, query ->
        query
        |> join(:inner, [q], m in assoc(q, :media))
        |> join(:left, [q, m], ma in MediaAnnouncement, on: m.id == ma.media_id)
        |> join(:left, [q, m, ma], mu in MediaUpdate, on: m.id == mu.media_id)
        |> where([q, m, ma, mu], not q.is_welcome_email and is_nil(ma.id) and is_nil(mu.id))

      %{key: "type", value: "shareholder_welcome"}, query ->
        query
        |> join(:inner, [q], m in assoc(q, :media))
        |> join(:left, [q, m], ma in MediaAnnouncement, on: m.id == ma.media_id)
        |> join(:left, [q, m, ma], mu in MediaUpdate, on: m.id == mu.media_id)
        |> where([q, m, ma, mu], q.is_welcome_email and is_nil(ma.id) and is_nil(mu.id))

      %{key: "type", value: "announcement"}, query ->
        query
        |> join(:inner, [q], m in assoc(q, :media))
        |> join(:left, [q, m], ma in MediaAnnouncement, on: m.id == ma.media_id)
        |> where([q, m, ma], not is_nil(ma.id))

      %{key: "type", value: "update"}, query ->
        query
        |> join(:inner, [q], m in assoc(q, :media))
        |> join(:left, [q, m], mu in MediaUpdate, on: m.id == mu.media_id)
        |> where([q, m, mu], not mu.invalidated and not is_nil(mu.id))

      %{key: "is_draft", value: "true"}, query ->
        from(q in query,
          where: q.is_draft == true
        )

      # TODO will need to add extra check once v2 is implemented, as scheduled introduced etc
      %{key: "is_draft", value: "false"}, query ->
        from(q in query,
          where: q.is_draft == false
        )

      %{key: "is_completed", value: "false"}, query ->
        from(q in query,
          where: is_nil(q.sent_at)
        )

      %{key: "is_completed", value: "true"}, query ->
        from(q in query,
          where: not is_nil(q.sent_at)
        )

      %{key: "is_scheduled", value: "false"}, query ->
        from(q in query,
          where: is_nil(q.scheduled_at)
        )

      %{key: "is_scheduled", value: "true"}, query ->
        from(q in query,
          where: not is_nil(q.scheduled_at) and is_nil(q.sent_at)
        )
    end)
  end

  defp emails_order_with(query, order) do
    Enum.reduce(order, query, fn
      %{key: "inserted_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :inserted_at])

      %{key: "inserted_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :inserted_at])

      %{key: "updated_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :updated_at])

      %{key: "updated_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :updated_at])

      %{key: "scheduled_at", value: "desc"}, query ->
        from(q in query, order_by: [desc: :scheduled_at])

      %{key: "scheduled_at", value: "asc"}, query ->
        from(q in query, order_by: [asc: :scheduled_at])

      %{key: "sent_at", value: "desc"}, query ->
        from(q in query,
          where: not is_nil(q.sent_at),
          order_by: [desc: :sent_at]
        )

      %{key: "sent_at", value: "asc"}, query ->
        from(q in query,
          where: not is_nil(q.sent_at),
          order_by: [asc: :sent_at]
        )

      %{key: "limit", value: n}, query ->
        from(q in query, limit: ^n)
    end)
  end

  def count_shareholder_welcome_emails_by_company_profile_id(company_profile_id) do
    EmailRecipient
    |> join(:inner, [er], e in assoc(er, :email))
    |> where(
      [er, e],
      e.is_welcome_email and not is_nil(e.sent_at) and e.company_profile_id == ^company_profile_id
    )
    |> Repo.aggregate(:count)
  end

  def get_email_type(%Email{is_welcome_email: is_welcome_email, media_id: media_id})
      when is_nil(media_id) and is_welcome_email,
      do: "Shareholder welcome"

  def get_email_type(%Email{is_welcome_email: is_welcome_email, media_id: media_id})
      when is_nil(media_id) and not is_welcome_email,
      do: "Campaign"

  def get_email_type(%Email{media: %Media{media_announcement: %MediaAnnouncement{}}}), do: "Announcement"

  def get_email_type(%Email{media: %Media{media_update: %MediaUpdate{}}}), do: "Update"

  def get_email_type(_), do: "Unknown"

  def get_email_distribution_method(%Email{media: %Media{email_distribution_method: email_distribution_method}}) do
    email_distribution_method
    |> Atom.to_string()
    |> String.capitalize()
  end

  def get_email_distribution_method(_), do: "Manual"

  def subscription_scope_from_email(%Email{media: %Media{media_announcement: %MediaAnnouncement{}}}), do: "announcement"

  def subscription_scope_from_email(%Email{media: %Media{media_update: %MediaUpdate{}}}), do: "activity_update"

  def subscription_scope_from_email(_), do: "general"

  @doc """
  Creates an email.

  ## Examples

      iex> create_email(%{field: value})
      {:ok, %Email{}}

      iex> create_email(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_email(attrs \\ %{}) do
    %Email{}
    |> Email.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an email.

  ## Examples

      iex> update_email(email, %{field: new_value})
      {:ok, %Email{}}

      iex> update_email(email, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_email(%Email{} = email, attrs) do
    email
    |> Email.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes an email.

  ## Examples

      iex> delete_email(email)
      {:ok, %Email{}}

      iex> delete_email(email)
      {:error, %Ecto.Changeset{}}

  """
  def delete_email(%Email{} = email) do
    Repo.delete(email)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking email changes.

  ## Examples

      iex> change_email(email)
      %Ecto.Changeset{data: %Email{}}

  """
  def change_email(%Email{} = email, attrs \\ %{}) do
    Email.changeset(email, attrs)
  end

  @doc """
  Return a list of draft emails that use the provided dynamic list id

  ## Examples

      iex> list_draft_emails_by_dynamic_list_id(1, 2)
      [%Email{}]

  """
  def list_draft_emails_by_dynamic_list_id(company_profile_id, dynamic_list_id) when is_integer(dynamic_list_id) do
    Email
    |> where(company_profile_id: ^company_profile_id)
    |> where(is_draft: true)
    |> where([e], is_nil(e.sent_at))
    |> where(
      [e],
      fragment(
        "? @> ?::jsonb OR ? @> ?::jsonb",
        e.send_to_dynamic_list_ids,
        ^[dynamic_list_id],
        e.do_not_send_to_dynamic_list_ids,
        ^[dynamic_list_id]
      )
    )
    |> Gaia.Repo.all()
  end

  def list_draft_emails_by_dynamic_list_id(company_profile_id, dynamic_list_id) when is_binary(dynamic_list_id) do
    {dynamic_list_id_int, _} = Integer.parse(dynamic_list_id)
    list_draft_emails_by_dynamic_list_id(company_profile_id, dynamic_list_id_int)
  end

  @doc """
  Return a list of draft emails that use the provided static list id

  ## Examples

      iex> list_draft_emails_by_static_list_id(1, 2)
      [%Email{}]

  """

  def list_draft_emails_by_static_list_id(company_profile_id, static_list_id) when is_integer(static_list_id) do
    Email
    |> where(company_profile_id: ^company_profile_id)
    |> where(is_draft: true)
    |> where([e], is_nil(e.sent_at))
    |> where(
      [e],
      fragment(
        "? @> ?::jsonb OR ? @> ?::jsonb",
        e.send_to_static_list_ids,
        ^[static_list_id],
        e.do_not_send_to_static_list_ids,
        ^[static_list_id]
      )
    )
    |> Gaia.Repo.all()
  end

  def list_draft_emails_by_static_list_id(company_profile_id, static_list_id) when is_binary(static_list_id) do
    {static_list_id_int, _} = Integer.parse(static_list_id)
    list_draft_emails_by_static_list_id(company_profile_id, static_list_id_int)
  end

  @doc """
  Returns the list of comms_email_recipients.

  ## Examples

      iex> list_comms_email_recipients()
      [%EmailRecipient{}, ...]

  """
  def list_comms_email_recipients do
    Repo.all(EmailRecipient)
  end

  @doc """
  Gets a single email recipient.

  Raises `Ecto.NoResultsError` if the EmailRecipient does not exist.

  ## Examples

      iex> get_email_recipient!(123)
      %EmailRecipient{}

      iex> get_email_recipient!(456)
      ** (Ecto.NoResultsError)

  """
  def get_email_recipient!(id), do: Repo.get!(EmailRecipient, id)
  def get_email_recipient(id), do: Repo.get(EmailRecipient, id)
  def get_email_recipient_by(args), do: Repo.get_by(EmailRecipient, args)

  @doc """
  Creates an email recipient.

  ## Examples

      iex> create_email_recipient(%{field: value})
      {:ok, %EmailRecipient{}}

      iex> create_email_recipient(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_email_recipient(attrs \\ %{}) do
    %EmailRecipient{}
    |> EmailRecipient.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an email recipient.

  ## Examples

      iex> update_email_recipient(email_recipient, %{field: new_value})
      {:ok, %EmailRecipient{}}

      iex> update_email_recipient(email_recipient, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_email_recipient(%EmailRecipient{} = email_recipient, attrs) do
    email_recipient
    |> EmailRecipient.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes an email recipient.

  ## Examples

      iex> delete_email_recipient(email_recipient)
      {:ok, %EmailRecipient{}}

      iex> delete_email_recipient(email_recipient)
      {:error, %Ecto.Changeset{}}

  """
  def delete_email_recipient(%EmailRecipient{} = email_recipient) do
    Repo.delete(email_recipient)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking email recipient changes.

  ## Examples

      iex> change_email_recipient(email_recipient)
      %Ecto.Changeset{data: %EmailRecipient{}}

  """
  def change_email_recipient(%EmailRecipient{} = email_recipient, attrs \\ %{}) do
    EmailRecipient.changeset(email_recipient, attrs)
  end

  def build_merge_tags_for_email(%Email{} = email, contact) do
    with {:preloaded_email,
          %Email{
            id: email_id,
            media: media,
            company_profile: %Profile{
              custom_domain: company_custom_domain,
              ticker: %Ticker{market_key: market_key, listing_key: listing_key} = ticker
            }
          }} <-
           {:preloaded_email,
            Repo.preload(email, [
              [company_profile: [:custom_emails, :ticker, :custom_domain]],
              [media: [:media_announcement, :media_update]]
            ])},
         {:base_url, base_url} <-
           {:base_url, Companies.get_company_base_url(company_custom_domain, ticker)},
         {:unsubscribe_scope, unsubscribe_scope} <-
           {:unsubscribe_scope, get_unsubscribe_scope_for_email(media)},
         {:unsubscribe_url, unsubscribe_url} <-
           {:unsubscribe_url,
            case contact do
              %Contact{id: contact_id} ->
                build_unsubscribe_url(base_url, email_id, contact_id, unsubscribe_scope)

              _ ->
                nil
            end} do
      first_name = Gaia.Emails.NameCleaner.generate(contact)

      merge_tags =
        media
        |> build_merge_tags_for_email_media(
          base_url,
          market_key,
          email_id
        )
        |> Map.merge(%{
          first_name: first_name,
          #  if link has not been changed, use investor hub url as default
          link: base_url,
          ticker: String.upcase(listing_key),
          market: market_key |> Atom.to_string() |> String.upcase()
        })

      case unsubscribe_url do
        nil ->
          Map.put(merge_tags, :unsubscribe_url, "#")

        _ ->
          Map.put(merge_tags, :unsubscribe_url, unsubscribe_url)
      end
    end
  end

  def get_unsubscribe_scope_for_email(%Media{
        media_announcement: %MediaAnnouncement{} = _media_announcement,
        media_update: media_update
      })
      when is_nil(media_update),
      do: "announcement"

  def get_unsubscribe_scope_for_email(%Media{
        media_announcement: media_announcement,
        media_update: %MediaUpdate{} = _media_update
      })
      when is_nil(media_announcement),
      do: "activity_update"

  def get_unsubscribe_scope_for_email(_media), do: "general"

  def build_unsubscribe_url(base_url, email_id, contact_id, scope \\ "general")

  def build_unsubscribe_url(base_url, nil, contact_id, scope) do
    base_url
    |> URI.parse()
    |> URI.merge("/unsubscribe/#{Helper.Hashid.encode_id(contact_id)}/#{scope}")
    |> URI.to_string()
  end

  def build_unsubscribe_url(base_url, email_id, contact_id, scope) do
    unsubscribe_url_query =
      URI.encode_query(%{"email_id" => Helper.Hashid.encode_id(email_id)}, :rfc3986)

    base_url
    |> URI.parse()
    |> URI.merge("/unsubscribe/#{Helper.Hashid.encode_id(contact_id)}/#{scope}")
    |> URI.append_query(unsubscribe_url_query)
    |> URI.to_string()
  end

  def build_one_click_unsubscribe_link(contact_id, scope) do
    base_url = Application.fetch_env!(:helper, :hermes_url)

    unsubscribe_url_query =
      URI.encode_query(
        %{
          "contact_hash_id" => Helper.Hashid.encode_id(contact_id),
          "scope" => scope
        },
        :rfc3986
      )

    base_url
    |> URI.parse()
    |> URI.merge("/public/unsubscribe")
    |> URI.append_query(unsubscribe_url_query)
    |> URI.to_string()
  end

  def build_one_click_unsubscribe_link(contact_id, email_id, media) do
    base_url = Application.fetch_env!(:helper, :hermes_url)
    scope = get_unsubscribe_scope_for_email(media)

    unsubscribe_url_query =
      URI.encode_query(
        %{
          "email_hash_id" => Helper.Hashid.encode_id(email_id),
          "contact_hash_id" => Helper.Hashid.encode_id(contact_id),
          "scope" => scope
        },
        :rfc3986
      )

    base_url
    |> URI.parse()
    |> URI.merge("/public/unsubscribe")
    |> URI.append_query(unsubscribe_url_query)
    |> URI.to_string()
  end

  def build_merge_tags_for_email_media(
        %Media{
          media_announcement: %MediaAnnouncement{id: announcement_id, header: announcement_title} = _media_announcement,
          media_update: media_update
        },
        base_url,
        market_key,
        email_id
      )
      when is_nil(media_update) do
    %{
      interactive_media_type: "announcement",
      interactive_media_title: announcement_title,
      interactive_media_url:
        "#{base_url}/announcements/#{announcement_id}?utm_medium=email&utm_source=distribution&utm_campaign=deid-#{email_id}",
      interactive_media_destination: "the #{market_key |> Atom.to_string() |> String.upcase()}"
    }
  end

  def build_merge_tags_for_email_media(
        %Media{
          media_announcement: media_announcement,
          media_update: %MediaUpdate{slug: update_slug, title: update_title} = _media_update
        },
        base_url,
        _market_key,
        email_id
      )
      when is_nil(media_announcement) do
    %{
      interactive_media_type: "update",
      interactive_media_destination: "our investor hub",
      interactive_media_title: update_title,
      interactive_media_url:
        "#{base_url}/activity-updates/#{update_slug}?utm_medium=email&utm_source=distribution&utm_campaign=deid-#{email_id}"
    }
  end

  def build_merge_tags_for_email_media(_media, _base_url, _market_key, _email_id), do: %{}

  def build_subject_merge_tags_for_email_media(%Media{
        media_announcement: %MediaAnnouncement{header: announcement_title} = _media_announcement,
        media_update: media_update
      })
      when is_nil(media_update) do
    %{
      interactive_media_title: announcement_title
    }
  end

  def build_subject_merge_tags_for_email_media(%Media{
        media_announcement: media_announcement,
        media_update: %MediaUpdate{title: update_title} = _media_update
      })
      when is_nil(media_announcement) do
    %{
      interactive_media_title: update_title
    }
  end

  def build_subject_merge_tags_for_email_media(_media), do: %{}

  @doc """
  Returns the list of email recipients for a given email id.

  ## Options

  - `:filters`: A list of filters to apply. Each filter should be a map with a `:key` and a `:value`.
    Available filters are:
    - `search`: text search
    - `deliveryStatus`: Filters recipients based on their delivery status. Options: "sending", "sent", "failed".
    - `unsubscribedFrom`: Filters recipients based on their delivery status. Global unsub is taken into account. Options: see scopes for contact_unsubscribe. eg "general"
    - `subscribedTo`: Filters recipients based on their delivery status. Global unsub is taken into account.Options: see scopes for contact_unsubscribe. eg "general"
    - `isOpened`: Filters recipients based on whether they have opened the email. Options: "true" or "false" (as strings).
    - `isClicked`: Filters recipients based on whether they have clicked on the email. Options: "true" or "false" (as strings).

  ## Examples

      iex> list_email_recipients(email_id)
      [%EmailRecipient{}, ...]

      iex> list_email_recipients(email_id, filters: [%{key: "isOpened", value: "true"}])
      [%EmailRecipient{}, ...]

  """
  def list_email_recipients(email_id, options \\ %{}) do
    email_id
    |> email_recipients_query(options)
    |> Repo.all()
    |> filter_by_tracking_email_statuses(options[:filters])
  end

  @doc """
  Returns the number of `%Contact{}` to be used as email recipients.

  ## Examples

      iex> count_email_recipients(%{}, "general")
      10

  """
  def count_email_recipients(
        %{
          company_profile_id: _,
          do_not_send_to_contact_ids: _,
          do_not_send_to_dynamic_list_ids: _,
          do_not_send_to_static_list_ids: _,
          send_to_all_contacts: _,
          send_to_contact_ids: _,
          send_to_dynamic_list_ids: _,
          send_to_static_list_ids: _
        } = input,
        subscription_scope
      ) do
    input
    |> generate_email_recipients_query(subscription_scope)
    |> Repo.aggregate(:count)
  end

  @doc """
  Returns a list of `%Contact{}` to be used as email recipients.

  ## Examples

      iex> generate_email_recipients(%{}, "general")
      [%Contact{}, ...]

  """
  def generate_email_recipients(
        %{
          company_profile_id: _,
          do_not_send_to_contact_ids: _,
          do_not_send_to_dynamic_list_ids: _,
          do_not_send_to_static_list_ids: _,
          send_to_all_contacts: _,
          send_to_contact_ids: _,
          send_to_dynamic_list_ids: _,
          send_to_static_list_ids: _
        } = input,
        subscription_scope
      ) do
    input
    |> generate_email_recipients_query(subscription_scope)
    |> Repo.all()
  end

  def generate_email_recipients_query(
        %{
          company_profile_id: company_profile_id,
          do_not_send_to_contact_ids: do_not_send_to_contact_ids,
          do_not_send_to_dynamic_list_ids: do_not_send_to_dynamic_list_ids,
          do_not_send_to_static_list_ids: do_not_send_to_static_list_ids,
          send_to_all_contacts: true
        },
        subscription_scope
      ) do
    exclusion_ids =
      company_profile_id
      |> Contacts.list_contact_ids_from_dynamic_list_ids(do_not_send_to_dynamic_list_ids)
      |> Kernel.++(do_not_send_to_contact_ids)

    exclusion_ids =
      company_profile_id
      |> Contacts.get_static_list_member_contact_ids_from_static_list_ids(do_not_send_to_static_list_ids)
      |> Kernel.++(exclusion_ids)
      |> Enum.uniq()
      |> Enum.join(",")

    options = %{
      filters: [
        %{key: "exclude_ids", value: exclusion_ids},
        %{key: "subscription_scope", value: subscription_scope},
        %{key: "has_email", value: "true"}
      ]
    }

    company_profile_id
    |> Contacts.contacts_query(options)
    |> subquery()
    |> distinct([c], c.id)
  end

  def generate_email_recipients_query(
        %{
          company_profile_id: company_profile_id,
          do_not_send_to_contact_ids: do_not_send_to_contact_ids,
          do_not_send_to_dynamic_list_ids: do_not_send_to_dynamic_list_ids,
          do_not_send_to_static_list_ids: do_not_send_to_static_list_ids,
          send_to_all_contacts: false,
          send_to_contact_ids: send_to_contact_ids,
          send_to_dynamic_list_ids: send_to_dynamic_list_ids,
          send_to_static_list_ids: send_to_static_list_ids
        },
        subscription_scope
      ) do
    exclusion_ids =
      company_profile_id
      |> Contacts.list_contact_ids_from_dynamic_list_ids(do_not_send_to_dynamic_list_ids)
      |> Kernel.++(do_not_send_to_contact_ids)

    exclusion_ids =
      company_profile_id
      |> Contacts.get_static_list_member_contact_ids_from_static_list_ids(do_not_send_to_static_list_ids)
      |> Kernel.++(exclusion_ids)
      |> Enum.uniq()
      |> Enum.join(",")

    inclusion_ids =
      company_profile_id
      |> Contacts.list_contact_ids_from_dynamic_list_ids(send_to_dynamic_list_ids)
      |> Kernel.++(send_to_contact_ids)

    inclusion_ids =
      company_profile_id
      |> Contacts.get_static_list_member_contact_ids_from_static_list_ids(send_to_static_list_ids)
      |> Kernel.++(inclusion_ids)
      |> Enum.uniq()
      |> Enum.join(",")

    options = %{
      filters: [
        %{key: "exclude_ids", value: exclusion_ids},
        %{key: "include_ids", value: inclusion_ids},
        %{key: "subscription_scope", value: subscription_scope},
        %{key: "has_email", value: "true"}
      ]
    }

    company_profile_id
    |> Contacts.contacts_query(options)
    |> subquery()
    |> distinct([c], c.id)
  end

  def email_recipients_query(email_id, options \\ %{}) do
    EmailRecipient
    |> from(as: :recipient)
    |> join(:inner, [recipient: r], c in assoc(r, :contact), as: :contact)
    |> join(:left, [recipient: r], s in assoc(r, :shareholder), as: :shareholder)
    |> join(:left, [recipient: r], te in assoc(r, :tracking_email), as: :tracking_email)
    |> join(:left, [tracking_email: te], tev in assoc(te, :events), as: :tracking_email_event)
    |> join(:left, [contact: c], cu in assoc(c, :comms_unsubscribes), as: :comms_unsubscribes)
    |> join(:left, [contact: c], gu in assoc(c, :global_unsubscribe), as: :global_unsubscribes)
    |> where([recipient: r], r.email_id == ^email_id)
    |> where(^filter_where(options[:filters] || []))
    |> preload(tracking_email: :events)
    |> order_by(^filter_order_by(options[:orders]))
    |> distinct(true)
  end

  def filter_order_by(%{key: "id", value: "asc"}), do: [asc: dynamic([recipient: r], r.id)]

  def filter_order_by(%{key: "id", value: "desc"}), do: [desc: dynamic([recipient: r], r.id)]

  def filter_order_by(_), do: []

  defp filter_where(filters) do
    Enum.reduce(filters, dynamic(true), &apply_filter/2)
  end

  defp filter_by_tracking_email_statuses(recipients, nil), do: recipients

  defp filter_by_tracking_email_statuses(recipients, options) do
    # Discard options where value is empty string:
    options = Enum.reject(options, fn %{key: _, value: value} -> value == "" end)

    Enum.reduce(options, recipients, fn
      %{key: "isOpened", value: value}, acc_recipients ->
        filter_by_event_type(acc_recipients, value == "true", :Open)

      %{key: "isClicked", value: value}, acc_recipients ->
        filter_by_event_type(acc_recipients, value == "true", :Click)

      _, acc_recipients ->
        acc_recipients
    end)
  end

  defp filter_by_event_type(recipients, state, event_type) do
    Enum.filter(recipients, fn
      %{tracking_email: %{events: events}} when is_list(events) ->
        Enum.any?(events, fn event ->
          event.event_type == event_type
        end) == state

      _recipient ->
        !state
    end)
  end

  defp apply_filter(%{key: "search", value: search_input}, dynamic) do
    search = "%#{search_input}%"

    # Need to handle two different cases
    # When recipient is a contact
    # When recipient is a shareholding (legacy behaviour)
    dynamic(
      [contact: c, shareholder: s],
      (^dynamic and
         (not is_nil(c.id) and
            (ilike(c.email, ^search) or
               ilike(
                 fragment("? || ' ' || ?", coalesce(c.first_name, ""), coalesce(c.last_name, "")),
                 ^search
               )))) or
        (not is_nil(s.id) and (ilike(s.email, ^search) or ilike(s.account_name, ^search)))
    )
  end

  defp apply_filter(%{key: "deliveryStatus", value: "sending"}, conditions) do
    dynamic(
      [],
      ^conditions and
        not exists(
          from(tev in Gaia.Tracking.EmailEvent,
            where:
              tev.event_type in [
                :Bounce,
                :Complaint,
                :Delivery,
                :Reject,
                :Open,
                :Click,
                :"Rendering Failure",
                :DeliveryDelay,
                :Subscription
              ],
            where: tev.email_id == parent_as(:tracking_email).id
          )
        )
    )
  end

  defp apply_filter(%{key: "deliveryStatus", value: "sent"}, current_conditions) do
    dynamic(
      [],
      ^current_conditions and
        exists(
          from(tev in Gaia.Tracking.EmailEvent,
            where: tev.event_type == :Delivery,
            where: tev.email_id == parent_as(:tracking_email).id
          )
        )
    )
  end

  defp apply_filter(%{key: "deliveryStatus", value: "failed"}, current_conditions) do
    dynamic(
      [],
      ^current_conditions and
        exists(
          from(tev in Gaia.Tracking.EmailEvent,
            where: tev.event_type in [:Bounce, :Complaint, :Reject, :"Rendering Failure"],
            where: tev.email_id == parent_as(:tracking_email).id
          )
        )
    )
  end

  defp apply_filter(%{key: "deliveryStatus", value: "bounce"}, current_conditions) do
    dynamic(
      [],
      ^current_conditions and
        exists(
          from(tev in Gaia.Tracking.EmailEvent,
            where: tev.event_type == :Bounce,
            where: tev.email_id == parent_as(:tracking_email).id
          )
        )
    )
  end

  defp apply_filter(%{key: "unsubscribedFrom", value: scope}, current_conditions) do
    dynamic(
      [comms_unsubscribes: cu, global_unsubscribes: gu],
      (cu.scope == ^String.to_existing_atom(scope) or not is_nil(gu.id)) and ^current_conditions
    )
  end

  defp apply_filter(%{key: "subscribedTo", value: scope}, current_conditions) do
    dynamic(
      [comms_unsubscribes: cu, global_unsubscribes: gu],
      (cu.scope != ^String.to_existing_atom(scope) or is_nil(cu.id)) and is_nil(gu.id) and
        ^current_conditions
    )
  end

  defp apply_filter(_, dynamic), do: dynamic
end
