defmodule Gaia.Repo.Migrations.CreateAdminAgentsQuestions do
  use Ecto.Migration

  def change do
    create table(:admin_agents_questions) do
      add :question, :text, null: false
      add :reason, :text
      add :is_done, :boolean, default: false, null: false
      add :admin_user_id, references(:admins_users, on_delete: :delete_all), null: false

      timestamps()
    end

    create index(:admin_agents_questions, [:admin_user_id])
    create index(:admin_agents_questions, [:is_done])
  end
end
