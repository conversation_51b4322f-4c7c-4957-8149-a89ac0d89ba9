defmodule Gaia.Repo.Migrations.BackfillContactSourceAusWithInvalidated do
  @moduledoc false
  use Ecto.Migration

  import Ecto.Query

  alias Gaia.Contacts.Contact

  @temp_table_name "temp_contacts_aus_invalidated"

  @query """
    SELECT
  cc.id AS contact_id,
  CASE WHEN cc.lead_identified_reason = 'subscribed' THEN
  	'subscribe_form' -- Anything with subscribed means it was not overridden by anything else, meaning we can safely assume it is subscribe_form
  WHEN cc.lead_identified_reason = 'created_contact'
  	AND(NOT cc.creator_name IS NULL
  		OR NOT cc.creator_user_id IS NULL) THEN
  	'manual_creation' -- When creating from Athena
  WHEN (NOT cc.creator_name IS NULL)
  	AND((cc.imported_at IS NULL)
  	OR(cc.inserted_at < cc.imported_at)) THEN
  	'manual_creation' -- It was manually created first if it has the creator name but an imported date is before the created date
  WHEN NOT iu.inserted_at IS NULL
  	AND iu.inserted_at <= cc.inserted_at THEN
  	'hub_signup' -- If the contact signp up for hub after being created
  WHEN NOT cc.imported_at IS NULL
  	AND cc.imported_at = cc.inserted_at THEN
  	'bulk_import' -- If the date of the creation of the contact is the same as the imported date
  WHEN NOT cc.imported_at IS NULL
  	AND(cc.inserted_at - cc.imported_at = '00:00:01') THEN
  	'bulk_import' -- Odd case where contact has 1 second difference between import and creating, could not find the reason why
  WHEN NOT esh.shareholding_linked_at IS NULL
  	AND esh.shareholding_linked_at = cc.inserted_at THEN
  	'registry_import' -- Registry shareholder connected to the contact and inserted/linked at same time
  WHEN NOT esh.shareholding_inserted_at IS NULL
  	AND esh.shareholding_inserted_at = cc.inserted_at THEN
  	'registry_import' -- Registry shareholder connected to the contact and both inserted at the same time
  	-- ==========================================================
  	-- === Low Confidence below, but only 12% of all contatcs ===
  	-- ==========================================================
  WHEN NOT cc.imported_at IS NULL
  	AND esh.shareholding_inserted_at IS NULL
  	AND iu.inserted_at IS NULL THEN
  	'bulk_import' -- Didn't fall under previous conditions has an imported date but no investor user or shareholding to it
  WHEN cc.imported_at IS NULL
  	AND iu.inserted_at IS NULL
  	AND esh.shareholding_inserted_at IS NULL THEN
  	'registry_import' -- All dates are null and didn't catch on previous conditions we are assuming it was created from registry import, but maybe company changed the registry
  WHEN (NOT iu.inserted_at IS NULL)
  	AND(iu.inserted_at - cc.inserted_at = '00:00:01') THEN
  	'hub_signup' -- All dates are null and didn't catch on previous conditions we are assuming it was created from registry import but maybe the company changed the registry
  WHEN (NOT esh.shareholding_inserted_at IS NULL)
  	AND(cc.imported_at IS NULL)
  	AND(iu.inserted_at IS NULL) THEN
  	'registry_import' -- Another odd case
  WHEN (NOT esh.shareholding_inserted_at IS NULL)
  	AND esh.shareholding_inserted_at <= cc.inserted_at
  	AND(cc.imported_at IS NULL)
  	AND(iu.inserted_at IS NULL) THEN
  	'registry_import' -- Didn't get caught from other conditions has a shareholder connected to id, but the inserted date is before the registry import
  WHEN (NOT cc.imported_at IS NULL)
  	AND(esh.shareholding_inserted_at IS NULL
  		OR(NOT esh.shareholding_inserted_at IS NULL
  			AND esh.shareholding_inserted_at > cc.inserted_at))
  	AND(iu.inserted_at IS NULL
  		OR(NOT iu.inserted_at IS NULL
  			AND iu.inserted_at > cc.inserted_at)) THEN
  	'bulk_import' -- Didn't get caught from other conditions has a shareholder connected to id, but the inserted date is before the registry import
  WHEN (NOT esh.shareholding_linked_at IS NULL)
  	AND esh.shareholding_linked_at - cc.inserted_at = '00:00:01' THEN
  	'registry_import' -- Odd one-second delay
  WHEN (NOT cc.imported_at IS NULL)
  	AND(esh.shareholding_linked_at IS NULL
  		OR(NOT esh.shareholding_linked_at IS NULL
  			AND esh.shareholding_linked_at > cc.inserted_at))
  	AND(iu.inserted_at IS NULL
  		OR(NOT iu.inserted_at IS NULL
  			AND iu.inserted_at > cc.inserted_at)) THEN
  	'bulk_import' -- Assuming is bulk import if contact was inserted after shareholding if it has shareholding connected to it, and if there is no investor user or the investor user was created after the contact
  WHEN (NOT iu.inserted_at IS NULL)
  	AND(esh.shareholding_inserted_at IS NULL) THEN
  	'hub_signup' -- no shareholding but it has a investor user
  WHEN (NOT iu.inserted_at IS NULL)
  	AND(NOT esh.shareholding_inserted_at IS NULL)
  	AND esh.shareholding_inserted_at > cc.inserted_at
  	AND esh.shareholding_linked_at IS NULL THEN
  	'hub_signup' -- no shareholding but it has a investor user
  WHEN (NOT iu.inserted_at IS NULL)
  	AND(NOT esh.shareholding_linked_at IS NULL)
  	AND(esh.shareholding_linked_at > cc.inserted_at) THEN
  	'hub_signup' -- no shareholding but it has a investor user
  END AS lead_source
  FROM
  contacts_contacts cc
  JOIN companies_profiles cp ON cp.id = cc.company_profile_id
  JOIN markets_tickers mt ON mt.company_profile_id = cp.id
  	AND mt.market_key = 'asx'
  JOIN LATERAL (
  	SELECT
  		MIN(inserted_at) AS shareholding_inserted_at,
  		MIN(contact_linked_at) AS shareholding_linked_at,
  		MIN(initial_purchase_date) AS initial_purchase_date
  	FROM
  		registers_shareholdings
  	WHERE
  		contact_id = cc.id
  		AND NOT initial_purchase_date IS NULL) AS esh ON TRUE
  LEFT JOIN investors_users iu ON iu.contact_id = cc.id
  WHERE cc.contact_source IS NULL
  """

  def up do
    repo().query!(
      """
      CREATE TABLE IF NOT EXISTS "#{@temp_table_name}" AS
      #{@query}
      """,
      [],
      log: :info,
      timeout: :infinity
    )

    create_if_not_exists index(@temp_table_name, [:contact_id])

    updates =
      from(r in @temp_table_name,
        select: %{contact_id: r.contact_id, contact_source: r.lead_source},
        order_by: [asc: r.contact_id]
      )
      |> repo().all()
      |> Enum.group_by(& &1.contact_source)

    Enum.map(updates, fn {lead_source, contact_rows} ->

      if is_nil(lead_source) do
        raise "Lead source is nil"
      end

      contact_source = String.to_atom(lead_source)
      contact_ids = Enum.map(contact_rows, & &1.contact_id)

      contacts = repo().all(from(c in Contact, where: c.id in ^contact_ids), with_invalidated: true)

      Enum.each(contacts, fn contact ->
        changeset = Contact.changeset(contact, %{contact_source: contact_source})
        repo().update!(changeset)
      end)
    end)
  end

  def down, do: :ok
end
