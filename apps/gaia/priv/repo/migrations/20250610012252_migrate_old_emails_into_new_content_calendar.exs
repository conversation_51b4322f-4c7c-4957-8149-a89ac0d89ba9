defmodule Gaia.Repo.Migrations.MigrateOldEmailsIntoNewContentCalendar do
  use Ecto.Migration

  def up do
    execute """
    DO $$
    BEGIN
    -- Step 1: Add temporary column
    ALTER TABLE interactions_medias ADD COLUMN comms_email_id BIGINT;

    -- Step 2: Insert into interactions_medias with comms_email_id
    INSERT INTO interactions_medias (
      invalidated, title, company_profile_id, created_by_profile_user_id,
      inserted_at, updated_at, primary_distribution, target_date,
      distribution_email_enabled, comms_email_id
    )
    SELECT
      ce.invalidated,
      ce.campaign_name,
      ce.company_profile_id,
      ce.last_updated_by,
      ce.inserted_at,
      ce.updated_at,
      'email',
      CASE
        WHEN ce.is_draft THEN (ce.inserted_at AT TIME ZONE 'UTC' AT TIME ZONE cp.timezone)::date
        WHEN ce.sent_at IS NOT NULL THEN (ce.sent_at AT TIME ZONE 'UTC' AT TIME ZONE cp.timezone)::date
        WHEN ce.scheduled_at IS NOT NULL THEN (ce.scheduled_at AT TIME ZONE 'UTC' AT TIME ZONE cp.timezone)::date
        ELSE (ce.inserted_at AT TIME ZONE 'UTC' AT TIME ZONE cp.timezone)::date
      END,
      true,
      ce.id
    FROM comms_emails ce
    JOIN companies_profiles cp ON ce.company_profile_id = cp.id
    WHERE ce.media_id IS NULL;

    -- Step 3: Link comms_emails to newly inserted media records
    UPDATE comms_emails ce
    SET media_id = im.id
    FROM interactions_medias im
    WHERE im.comms_email_id IS NOT NULL
    AND ce.id = im.comms_email_id;

    -- Step 4: Drop temporary column
    ALTER TABLE interactions_medias DROP COLUMN comms_email_id;
    END $$;
    """
  end

  def down do
    execute """
    DO $$
    BEGIN
      -- Step 1: Unlink media_id from comms_emails
      UPDATE comms_emails ce
      SET media_id = NULL
      WHERE ce.media_id IN (
        SELECT im.id
        FROM interactions_medias im
        LEFT JOIN interactions_media_announcements ima ON im.id = ima.media_id
        LEFT JOIN interactions_media_updates imu ON im.id = imu.media_id
        WHERE ima.id IS NULL
          AND imu.id IS NULL
          AND im.distribution_email_enabled = TRUE
      );

      -- Step 2: Delete truly orphaned medias (after unlinking)
      DELETE FROM interactions_medias im
      WHERE im.id IN (
        SELECT im.id
        FROM interactions_medias im
        LEFT JOIN interactions_media_announcements ima ON im.id = ima.media_id
        LEFT JOIN interactions_media_updates imu ON im.id = imu.media_id
        WHERE ima.id IS NULL
          AND imu.id IS NULL
          AND im.distribution_email_enabled = TRUE
      )
      AND NOT EXISTS (
        SELECT 1 FROM comms_emails ce
        WHERE ce.media_id = im.id
      );
    END $$;
    """
  end
end
