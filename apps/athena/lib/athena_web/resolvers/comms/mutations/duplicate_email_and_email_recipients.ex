defmodule AthenaWeb.Resolvers.Comms.Mutations.DuplicateEmailAndEmailRecipients do
  @moduledoc """
    DuplicateEmailAndEmailRecipients Mutation Resolvers
  """

  alias Gaia.Comms
  alias Gaia.Comms.Email
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser

  def resolve(_, %{id: email_id}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            id: company_profile_user_id,
            profile: %Profile{id: current_company_profile_id}
          }
        }
      }) do
    with {:get_email, %Email{company_profile_id: company_profile_id} = email} <-
           {:get_email, Comms.get_email(email_id)},
         {:check_ownership, true} <-
           {:check_ownership, company_profile_id == current_company_profile_id},
         {:duplicate_email_and_email_recipients, {:ok, %Email{} = duplicated_email}} <-
           {:duplicate_email_and_email_recipients, duplicate_email(email, company_profile_user_id)} do
      # We do not need to duplicate the recipients
      # The current system populate recipients at send time based on the configuration on the `comms_emails`
      {:ok, duplicated_email}
    else
      error ->
        {
          :error,
          %Helper.AbsintheError{
            error: error,
            message:
              "Unfortunately we could not duplicate an email campaign at this time, please try again later or contact us for support."
          }
        }
    end
  end

  defp duplicate_email(%Email{} = email, company_profile_user_id) do
    email
    |> Map.merge(%{
      id: nil,
      sent_at: nil,
      scheduled_at: nil,
      is_draft: true,
      last_updated_by: company_profile_user_id,
      inserted_at: NaiveDateTime.utc_now(:second),
      updated_at: NaiveDateTime.utc_now(:second),
      media_id: nil
    })
    |> Map.from_struct()
    |> Map.drop([:__meta__, :__struct__])
    |> Comms.create_email()
  end
end
