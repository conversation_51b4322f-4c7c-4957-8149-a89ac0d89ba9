defmodule AthenaWeb.Resolvers.Contacts.ContactActivitiesV2 do
  @moduledoc """
  ContactActivities Query Resolvers
  """
  use Helper.Pipe

  import Ecto.Query

  alias Absinthe.Relay.Connection
  alias Gaia.Contacts
  alias Gaia.Contacts.Contact
  alias Gaia.Repo

  def resolve(_, %{contact_id: contact_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            permissions: permissions,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters = update_filters_company_profile_id(args)
    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)
    options = Map.put(options, :permissions, permissions)

    with %Contact{} = contact <-
           %{id: contact_id, company_profile_id: company_profile_id}
           |> Contacts.get_contact_by()
           |> Repo.preload([
             :investor,
             :shareholdings,
             company_profile: [:ticker],
             beneficial_owner_accounts: [:beneficial_owner_holdings]
           ]),
         {:ok, connection} <-
           contact
           |> Contacts.contact_activities_query(options)
           |> Contacts.group_activities_by_category()
           |> Contacts.group_activities_by_month()
           |> Connection.from_query(&Repo.all/1, args) do
      connection =
        update_in(connection[:edges], fn edges ->
          Enum.map(edges, fn edge -> put_in(edge[:node], atomize_keys(edge[:node], 3)) end)
        end)

      {:ok, %{edges: connection.edges, page_info: connection.page_info, options: options}}
    else
      nil ->
        {:error, "Contact not found"}

      {:error, error} ->
        {:error, %Helper.AbsintheError{error: error, message: "Error retrieving activities"}}
    end
  end

  def resolve(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving activities"
     }}
  end

  def total(_, %{contact_id: contact_id} = args, %{
        context: %{
          current_company_profile_user: %Gaia.Companies.ProfileUser{
            permissions: permissions,
            profile: %Gaia.Companies.Profile{id: company_profile_id}
          }
        }
      }) do
    filters = update_filters_company_profile_id(args)
    options = args |> Map.get(:options, %{}) |> Map.put(:filters, filters)
    options = Map.put(options, :permissions, permissions)

    %{id: contact_id, company_profile_id: company_profile_id}
    |> Contacts.get_contact_by()
    |> Repo.preload([
      :investor,
      :shareholdings,
      company_profile: [:ticker],
      beneficial_owner_accounts: [:beneficial_owner_holdings]
    ])
    |> case do
      %Contact{} = contact ->
        contact
        |> Contacts.contact_activities_query(options)
        |> Contacts.group_activities_by_category()
        |> Contacts.group_activities_by_month()
        # final subquery wrapping here since we can't use aggregation directly on a query with a group_by clause
        # since it's trying to count across groups, which isn't supported
        # so a workaround to wrap it in a subquery and then count the number of rows from that subquery
        |> subquery()
        |> Repo.aggregate(:count)
        |> {:ok, __}

      nil ->
        {:error, "Contact not found"}
    end
  end

  def total(_, _, _) do
    {:error,
     %Helper.AbsintheError{
       error: "Pattern match not found",
       message: "Error retrieving total activities"
     }}
  end

  defp update_filters_company_profile_id(args) do
    args
    |> Map.get(:options, %{})
    |> Map.get(:filters, [])
    |> Enum.filter(&(&1.key != "company_profile_id"))
  end

  defp atomize_keys(value, 0), do: value

  defp atomize_keys(map, depth) when is_map(map) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      atom_key =
        case key do
          binary when is_binary(binary) ->
            String.to_atom(binary)

          other ->
            other
        end

      new_value =
        cond do
          # not is_struct/1 to avoid atomizing structs like NaiveDateTime
          is_map(value) and not is_struct(value) -> atomize_keys(value, depth - 1)
          is_list(value) -> Enum.map(value, fn item -> atomize_keys(item, depth - 1) end)
          true -> value
        end

      Map.put(acc, atom_key, new_value)
    end)
  end

  defp atomize_keys(value, _), do: value
end
