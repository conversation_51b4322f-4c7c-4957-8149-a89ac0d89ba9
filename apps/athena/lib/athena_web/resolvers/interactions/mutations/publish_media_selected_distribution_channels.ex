defmodule AthenaWeb.Resolvers.Interactions.PublishMediaSelectedDistributionChannels do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.Interactions.PreparedAnnouncement
  alias Gaia.Interactions.SocialPost
  alias Gaia.ObanHelper
  alias Gaia.Repo

  require Logger

  def resolve(_parent, %{media_id: media_id, schedule_options: schedule_options}, %{
        context: %{
          current_company_profile_user: %ProfileUser{
            profile_id: current_company_profile_id,
            user_id: current_company_user_id
          }
        }
      }) do
    with %Media{} = media <-
           media_id
           |> Interactions.get_media()
           |> Repo.preload([
             :email,
             :prepared_announcement,
             :linkedin_social_post,
             :twitter_social_post,
             :media_update
           ]),
         true <- media_permitted?(media, current_company_profile_id),
         {:ok, _} <-
           maybe_publish_announcement(
             media,
             get_distribution_channel_schedule_options("announcement", schedule_options)
           ),
         {:ok, _} <-
           maybe_publish_update(
             media,
             get_distribution_channel_schedule_options("update", schedule_options),
             current_company_user_id
           ),
         {:ok, _} <-
           maybe_publish_linkedin(
             media,
             get_distribution_channel_schedule_options("linkedin", schedule_options)
           ),
         {:ok, _} <-
           maybe_publish_twitter(
             media,
             get_distribution_channel_schedule_options("twitter", schedule_options)
           ),
         {:ok, _} <-
           maybe_publish_email(
             media,
             get_distribution_channel_schedule_options("email", schedule_options)
           ),
         {:ok, updated_media} <-
           Interactions.update_media(media, %{state: :published}) do
      {:ok, updated_media}
    else
      nil ->
        {:error, "Media not found."}

      false ->
        {:error, "You are not authorised to publish this media."}

      _error ->
        {:error, "Oops! Something went wrong."}
    end
  end

  defp media_permitted?(%Media{company_profile_id: company_profile_id}, current_company_profile_id) do
    company_profile_id == current_company_profile_id
  end

  defp maybe_publish_announcement(%Media{prepared_announcement: %PreparedAnnouncement{} = prepared_announcement}, %{
         publish_now: false,
         publish_with_announcement: true
       }) do
    Interactions.update_prepared_announcement(prepared_announcement, %{is_draft: false})
  end

  defp maybe_publish_announcement(_, _), do: {:ok, nil}

  defp maybe_publish_update(
         %Media{distribution_update_enabled: true} = media,
         %{publish_now: true, publish_with_announcement: false},
         current_company_user_id
       ) do
    case Interactions.update_or_publish_media_update(media.media_update, current_company_user_id) do
      {:ok, _} -> {:ok, nil}
      nil -> {:ok, nil}
    end
  end

  defp maybe_publish_update(
         %Media{id: media_id, distribution_update_enabled: true} = media,
         %{publish_now: false, publish_with_announcement: false, schedule_at: schedule_at} = _schedule_option,
         current_company_user_id
       ) do
    # clear any existing scheduled jobs
    ObanHelper.cancel_media_distribution_jobs(media_id, "Gaia.Jobs.PublishMediaUpdate")

    Gaia.Jobs.PublishMediaUpdate.enqueue(
      %{
        "media_id" => media_id,
        "posted_by_id" => current_company_user_id,
        "schedule_at" => schedule_at
      },
      scheduled_at: DateTime.from_naive!(schedule_at, "Etc/UTC")
    )

    # TODO: should we update the schedule_at even after update got modified
    Interactions.update_media_update(media.media_update, %{scheduled_at: schedule_at})
  end

  defp maybe_publish_update(_, _, _), do: {:ok, nil}

  defp maybe_publish_linkedin(%Media{distribution_linkedin_enabled: true} = media, %{
         publish_now: true,
         publish_with_announcement: false
       }) do
    with %Media{linkedin_social_post: %SocialPost{} = linkedin_social_post} = media <-
           Repo.preload(media, [:linkedin_social_post, company_profile: [:social_connection]]),
         {:ok, _} <-
           Interactions.publish_social_post(media, linkedin_social_post) do
      {:ok, nil}
    else
      error ->
        Logger.error("Error publishing LinkedIn: #{inspect(error)}")
        {:error, "Oops! Something went wrong."}
    end
  end

  defp maybe_publish_linkedin(%Media{id: media_id, distribution_linkedin_enabled: true} = media, %{
         publish_now: false,
         publish_with_announcement: false,
         schedule_at: schedule_at
       }) do
    ObanHelper.cancel_media_distribution_jobs(media_id, "Gaia.Jobs.PublishMediaLinkedin")

    Gaia.Jobs.PublishMediaLinkedin.enqueue(%{"media_id" => media_id},
      scheduled_at: DateTime.from_naive!(schedule_at, "Etc/UTC")
    )

    Interactions.update_social_post(media.linkedin_social_post, %{
      scheduled_at: schedule_at,
      status: :scheduled
    })
  end

  defp maybe_publish_linkedin(_, _), do: {:ok, nil}

  defp maybe_publish_twitter(%Media{distribution_twitter_enabled: true} = media, %{
         publish_now: true,
         publish_with_announcement: false
       }) do
    with %Media{twitter_social_post: %SocialPost{} = twitter_social_post} = media <-
           Repo.preload(media, [:twitter_social_post, company_profile: [:social_connection]]),
         {:ok, _} <-
           Interactions.publish_social_post(media, twitter_social_post) do
      {:ok, nil}
    else
      error ->
        Logger.error("Error publishing Twitter: #{inspect(error)}")
        {:error, "Oops! Something went wrong."}
    end
  end

  defp maybe_publish_twitter(%Media{id: media_id, distribution_twitter_enabled: true} = media, %{
         publish_now: false,
         publish_with_announcement: false,
         schedule_at: schedule_at
       }) do
    ObanHelper.cancel_media_distribution_jobs(media_id, "Gaia.Jobs.PublishMediaTwitter")

    Gaia.Jobs.PublishMediaTwitter.enqueue(%{"media_id" => media_id},
      scheduled_at: DateTime.from_naive!(schedule_at, "Etc/UTC")
    )

    Interactions.update_social_post(media.twitter_social_post, %{
      scheduled_at: schedule_at,
      status: :scheduled
    })
  end

  defp maybe_publish_twitter(_, _), do: {:ok, nil}

  defp maybe_publish_email(%Media{id: media_id, distribution_email_enabled: true}, %{
         publish_now: true,
         publish_with_announcement: false
       }) do
    Gaia.Jobs.PublishMediaEmail.enqueue(%{"media_id" => media_id},
      scheduled_at: DateTime.utc_now()
    )
  end

  defp maybe_publish_email(%Media{id: media_id, distribution_email_enabled: true} = media, %{
         publish_now: false,
         publish_with_announcement: false,
         schedule_at: schedule_at
       }) do
    ObanHelper.cancel_media_distribution_jobs(media_id, "Gaia.Jobs.PublishMediaEmail")

    Gaia.Jobs.PublishMediaEmail.enqueue(%{"media_id" => media_id},
      scheduled_at: DateTime.from_naive!(schedule_at, "Etc/UTC")
    )

    Gaia.Comms.update_email(media.email, %{
      scheduled_at: schedule_at
    })
  end

  defp maybe_publish_email(_, _), do: {:ok, nil}

  defp get_distribution_channel_schedule_options(channel, schedule_options) do
    Enum.find(schedule_options, fn option -> option.channel == channel end)
  end
end
