defmodule AthenaWeb.Resolvers.Interactions.CancelScheduledMediaDistribution do
  @moduledoc false

  alias Gaia.Companies.ProfileUser
  alias Gaia.Interactions
  alias Gaia.Interactions.Media
  alias Gaia.ObanHelper

  def resolve(_parent, %{media_id: media_id, distribution_channel: distribution_channel}, %{
        context: %{current_company_profile_user: %ProfileUser{profile_id: current_company_profile_id}}
      }) do
    with %Media{} = media <-
           media_id
           |> Interactions.get_media()
           |> Gaia.Repo.preload([:email, :linkedin_social_post, :twitter_social_post, :media_update]),
         true <- media_permitted?(media, current_company_profile_id),
         job_module = get_distribution_channel_job_module(distribution_channel),
         cancelled_jobs =
           ObanHelper.cancel_media_distribution_jobs(media_id, job_module),
         true <- is_list(cancelled_jobs) do
      case distribution_channel do
        "update" ->
          Interactions.update_media_update(media.media_update, %{scheduled_at: nil})

        "linkedin" ->
          Interactions.update_social_post(media.linkedin_social_post, %{scheduled_at: nil})

        "twitter" ->
          Interactions.update_social_post(media.twitter_social_post, %{scheduled_at: nil})

        "email" ->
          Gaia.Comms.update_email(media.email, %{scheduled_at: nil})
      end

      {:ok, media}
    else
      nil ->
        {:error, "Media not found"}

      _ ->
        {:error, "Oops, something went wrong"}
    end
  end

  defp media_permitted?(%Media{company_profile_id: company_profile_id}, current_company_profile_id) do
    company_profile_id == current_company_profile_id
  end

  defp get_distribution_channel_job_module(distribution_channel) do
    case distribution_channel do
      "update" -> "Gaia.Jobs.PublishMediaUpdate"
      "linkedin" -> "Gaia.Jobs.PublishMediaLinkedin"
      "twitter" -> "Gaia.Jobs.PublishMediaTwitter"
      "email" -> "Gaia.Jobs.PublishMediaEmail"
      _ -> raise "Invalid distribution channel"
    end
  end
end
