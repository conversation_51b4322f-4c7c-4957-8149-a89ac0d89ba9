defmodule AthenaWeb.Workflows.SendEmailTest do
  use AthenaWeb.ConnCase, async: true

  @email_html "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional //EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\">\n<head>\n<!--[if gte mso 9]>\n<xml>\n  <o:OfficeDocumentSettings>\n    <o:AllowPNG/>\n    <o:PixelsPerInch>96</o:PixelsPerInch>\n  </o:OfficeDocumentSettings>\n</xml>\n<![endif]-->\n  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <meta name=\"x-apple-disable-message-reformatting\">\n  <!--[if !mso]><!--><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><!--<![endif]-->\n  <title></title>\n  \n    <style type=\"text/css\">\n      @media only screen and (min-width: 620px) {\n  .u-row {\n    width: 600px !important;\n  }\n  .u-row .u-col {\n    vertical-align: top;\n  }\n\n  .u-row .u-col-100 {\n    width: 600px !important;\n  }\n\n}\n\n@media (max-width: 620px) {\n  .u-row-container {\n    max-width: 100% !important;\n    padding-left: 0px !important;\n    padding-right: 0px !important;\n  }\n  .u-row .u-col {\n    min-width: 320px !important;\n    max-width: 100% !important;\n    display: block !important;\n  }\n  .u-row {\n    width: 100% !important;\n  }\n  .u-col {\n    width: 100% !important;\n  }\n  .u-col > div {\n    margin: 0 auto;\n  }\n}\nbody {\n  margin: 0;\n  padding: 0;\n}\n\ntable,\ntr,\ntd {\n  vertical-align: top;\n  border-collapse: collapse;\n}\n\np {\n  margin: 0;\n}\n\n.ie-container table,\n.mso-container table {\n  table-layout: fixed;\n}\n\n* {\n  line-height: inherit;\n}\n\na[x-apple-data-detectors='true'] {\n  color: inherit !important;\n  text-decoration: none !important;\n}\n\ntable, td { color: #1a1a1a; } #u_body a { color: #1a1a1a; text-decoration: none; }\n    </style>\n  \n  \n\n<!--[if !mso]><!--><link href=\"https://fonts.googleapis.com/css2?family=Roboto&display=swap\" rel=\"stylesheet\" type=\"text/css\"><!--<![endif]-->\n\n</head>\n\n<body class=\"clean-body u_body\" style=\"margin: 0;padding: 0;-webkit-text-size-adjust: 100%;background-color: #f9fafb;color: #1a1a1a\">\n  <!--[if IE]><div class=\"ie-container\"><![endif]-->\n  <!--[if mso]><div class=\"mso-container\"><![endif]-->\n  <table id=\"u_body\" style=\"border-collapse: collapse;table-layout: fixed;border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;vertical-align: top;min-width: 320px;Margin: 0 auto;background-color: #f9fafb;width:100%\" cellpadding=\"0\" cellspacing=\"0\">\n  <tbody>\n  <tr style=\"vertical-align: top\">\n    <td style=\"word-break: break-word;border-collapse: collapse !important;vertical-align: top\">\n    <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td align=\"center\" style=\"background-color: #f9fafb;\"><![endif]-->\n    \n  \n  \n<div id=\"email_template_body\" class=\"u-row-container email_template_body\" style=\"padding: 0px;background-color: transparent\">\n  <div class=\"u-row\" style=\"margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;\">\n    <div style=\"border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;\">\n      <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding: 0px;background-color: transparent;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:600px;\"><tr style=\"background-color: transparent;\"><![endif]-->\n      \n<!--[if (mso)|(IE)]><td align=\"center\" width=\"600\" style=\"background-color: #ffffff;width: 600px;padding: 32px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;\">\n  <div style=\"background-color: #ffffff;height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\">\n  <!--[if (!mso)&(!IE)]><!--><div style=\"box-sizing: border-box; height: 100%; padding: 32px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\"><!--<![endif]-->\n  \n<table style=\"font-family:'Roboto', sans-serif;\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" border=\"0\">\n  <tbody>\n    <tr>\n      <td style=\"overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Roboto', sans-serif;\" align=\"left\">\n        \n  <div style=\"font-size: 14px; line-height: 140%; text-align: left; word-wrap: break-word;\">\n    <p style=\"line-height: 140%;\"><span style=\"line-height: 19.6px;\">This is a new Text block. Change the text.</span></p>\n  </div>\n\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n  <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->\n  </div>\n</div>\n<!--[if (mso)|(IE)]></td><![endif]-->\n      <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->\n    </div>\n  </div>\n  </div>\n  \n\n\n  \n  \n<div id=\"email_template_footer\" class=\"u-row-container email_template_footer\" style=\"padding: 0px;background-color: transparent\">\n  <div class=\"u-row\" style=\"margin: 0 auto;min-width: 320px;max-width: 600px;overflow-wrap: break-word;word-wrap: break-word;word-break: break-word;background-color: transparent;\">\n    <div style=\"border-collapse: collapse;display: table;width: 100%;height: 100%;background-color: transparent;\">\n      <!--[if (mso)|(IE)]><table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td style=\"padding: 0px;background-color: transparent;\" align=\"center\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"width:600px;\"><tr style=\"background-color: transparent;\"><![endif]-->\n      \n<!--[if (mso)|(IE)]><td align=\"center\" width=\"600\" style=\"width: 600px;padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\" valign=\"top\"><![endif]-->\n<div class=\"u-col u-col-100\" style=\"max-width: 320px;min-width: 600px;display: table-cell;vertical-align: top;\">\n  <div style=\"height: 100%;width: 100% !important;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\">\n  <!--[if (!mso)&(!IE)]><!--><div style=\"box-sizing: border-box; height: 100%; padding: 0px;border-top: 0px solid transparent;border-left: 0px solid transparent;border-right: 0px solid transparent;border-bottom: 0px solid transparent;border-radius: 0px;-webkit-border-radius: 0px; -moz-border-radius: 0px;\"><!--<![endif]-->\n  \n<table style=\"font-family:'Roboto', sans-serif;\" role=\"presentation\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" border=\"0\">\n  <tbody>\n    <tr>\n      <td style=\"overflow-wrap:break-word;word-break:break-word;padding:10px;font-family:'Roboto', sans-serif;\" align=\"left\">\n        \n  <div style=\"font-size: 14px; line-height: 140%; text-align: center; word-wrap: break-word;\">\n    <p style=\"font-size: 14px; line-height: 140%; text-align: center;\"><span style=\"font-size: 14px; line-height: 19.599999999999998px; color: #7f8c8c;\"><span style=\"color: #7e8c8d; font-size: 14px; line-height: 19.6px;\"><a rel=\"noopener\" href=\"{{ unsubscribe_url }}\" target=\"_blank\" style=\"color: #7e8c8d;\">Unsubscribe</a></span> <span style=\"color: #000000; font-size: 14px; line-height: 19.6px;\">from these emails</span></span></p>\n  </div>\n\n      </td>\n    </tr>\n  </tbody>\n</table>\n\n  <!--[if (!mso)&(!IE)]><!--></div><!--<![endif]-->\n  </div>\n</div>\n<!--[if (mso)|(IE)]></td><![endif]-->\n      <!--[if (mso)|(IE)]></tr></table></td></tr></table><![endif]-->\n    </div>\n  </div>\n  </div>\n  \n\n\n    <!--[if (mso)|(IE)]></td></tr></table><![endif]-->\n    </td>\n  </tr>\n  </tbody>\n  </table>\n  <!--[if mso]></div><![endif]-->\n  <!--[if IE]></div><![endif]-->\n</body>\n\n</html>\n"

  @email_json "{\"counters\":{\"u_row\":9,\"u_column\":11,\"u_content_html\":3,\"u_content_text\":25,\"u_content_image\":2,\"u_content_button\":2,\"u_content_social\":3,\"u_content_divider\":2},\"body\":{\"id\":\"20b2WVuj4e\",\"rows\":[{\"id\":\"BNmTw9vXXh\",\"cells\":[1],\"columns\":[{\"id\":\"--OtQHdw5N\",\"contents\":[{\"id\":\"mf1DOFniLc\",\"type\":\"text\",\"values\":{\"containerPadding\":\"10px\",\"anchor\":\"\",\"fontSize\":\"14px\",\"textAlign\":\"left\",\"lineHeight\":\"140%\",\"linkStyle\":{\"inherit\":true,\"linkColor\":\"#0000ee\",\"linkHoverColor\":\"#0000ee\",\"linkUnderline\":true,\"linkHoverUnderline\":true},\"displayCondition\":null,\"_meta\":{\"htmlID\":\"u_content_text_25\",\"htmlClassNames\":\"u_content_text\"},\"selectable\":true,\"draggable\":true,\"duplicatable\":true,\"deletable\":true,\"hideable\":true,\"text\":\"<p style=\\\"line-height: 140%;\\\"><span style=\\\"line-height: 19.6px;\\\">This is a new Text block. Change the text.</span></p>\"}}],\"values\":{\"_meta\":{\"htmlID\":\"u_column_3\",\"htmlClassNames\":\"u_column\"},\"border\":{},\"padding\":\"32px\",\"borderRadius\":\"0px\",\"backgroundColor\":\"#ffffff\"}}],\"values\":{\"displayCondition\":null,\"columns\":false,\"backgroundColor\":\"\",\"columnsBackgroundColor\":\"\",\"backgroundImage\":{\"url\":\"\",\"fullWidth\":true,\"repeat\":\"no-repeat\",\"size\":\"custom\",\"position\":\"top-center\",\"customPosition\":[\"50%\",\"0%\"]},\"padding\":\"0px\",\"anchor\":\"\",\"hideDesktop\":false,\"_meta\":{\"htmlID\":\"email_template_body\",\"htmlClassNames\":\"email_template_body\"},\"selectable\":true,\"draggable\":true,\"duplicatable\":true,\"deletable\":false,\"hideable\":true}},{\"id\":\"ghHpg1rhsN\",\"cells\":[1],\"columns\":[{\"id\":\"BjIVR6Bl8_\",\"contents\":[{\"id\":\"KVGxWFuvPS\",\"type\":\"text\",\"values\":{\"containerPadding\":\"10px\",\"anchor\":\"\",\"fontSize\":\"14px\",\"textAlign\":\"center\",\"lineHeight\":\"140%\",\"linkStyle\":{\"inherit\":true,\"linkColor\":\"#0000ee\",\"linkHoverColor\":\"#0000ee\",\"linkUnderline\":true,\"linkHoverUnderline\":true},\"hideDesktop\":false,\"displayCondition\":null,\"_meta\":{\"htmlID\":\"u_content_text_16\",\"htmlClassNames\":\"u_content_text\"},\"selectable\":true,\"draggable\":true,\"duplicatable\":true,\"deletable\":true,\"hideable\":true,\"text\":\"<p style=\\\"font-size: 14px; line-height: 140%; text-align: center;\\\"><span style=\\\"font-size: 14px; line-height: 19.599999999999998px; color: #7f8c8c;\\\"><span style=\\\"color: #7e8c8d; font-size: 14px; line-height: 19.6px;\\\"><a rel=\\\"noopener\\\" href=\\\"{{ unsubscribe_url }}\\\" target=\\\"_blank\\\" style=\\\"color: #7e8c8d;\\\" data-u-link-value=\\\"eyJuYW1lIjoid2ViIiwiYXR0cnMiOnsiaHJlZiI6Int7aHJlZn19IiwidGFyZ2V0Ijoie3t0YXJnZXR9fSJ9LCJ2YWx1ZXMiOnsiaHJlZiI6Int7IHVuc3Vic2NyaWJlX3VybCB9fSIsInRhcmdldCI6Il9ibGFuayJ9fQ==\\\">Unsubscribe</a></span> <span style=\\\"color: #000000; font-size: 14px; line-height: 19.6px;\\\">from these emails</span></span></p>\"},\"hasDeprecatedFontControls\":true}],\"values\":{\"_meta\":{\"htmlID\":\"u_column_7\",\"htmlClassNames\":\"u_column\"},\"border\":{},\"padding\":\"0px\",\"borderRadius\":\"0px\",\"backgroundColor\":\"\"}}],\"values\":{\"displayCondition\":null,\"columns\":false,\"backgroundColor\":\"\",\"columnsBackgroundColor\":\"\",\"backgroundImage\":{\"url\":\"\",\"fullWidth\":true,\"repeat\":\"no-repeat\",\"size\":\"custom\",\"position\":\"top-center\",\"customPosition\":[\"50%\",\"0%\"]},\"padding\":\"0px\",\"anchor\":\"\",\"hideDesktop\":false,\"_meta\":{\"htmlID\":\"email_template_footer\",\"htmlClassNames\":\"email_template_footer\"},\"selectable\":true,\"draggable\":true,\"duplicatable\":true,\"deletable\":true,\"hideable\":true}}],\"headers\":[],\"footers\":[],\"values\":{\"popupPosition\":\"center\",\"popupWidth\":\"600px\",\"popupHeight\":\"auto\",\"borderRadius\":\"10px\",\"contentAlign\":\"center\",\"contentVerticalAlign\":\"center\",\"contentWidth\":\"600px\",\"fontFamily\":{\"label\":\"Roboto\",\"value\":\"'Roboto', sans-serif\",\"url\":\"https://fonts.googleapis.com/css2?family=Roboto&display=swap\",\"defaultFont\":false},\"textColor\":\"#1a1a1a\",\"popupBackgroundColor\":\"#FFFFFF\",\"popupBackgroundImage\":{\"url\":\"\",\"fullWidth\":true,\"repeat\":\"no-repeat\",\"size\":\"cover\",\"position\":\"top-center\",\"customPosition\":[\"50%\",\"0%\"]},\"popupOverlay_backgroundColor\":\"rgba(0, 0, 0, 0.1)\",\"popupCloseButton_position\":\"top-right\",\"popupCloseButton_backgroundColor\":\"#DDDDDD\",\"popupCloseButton_iconColor\":\"#000000\",\"popupCloseButton_borderRadius\":\"0px\",\"popupCloseButton_margin\":\"0px\",\"popupCloseButton_action\":{\"name\":\"close_popup\",\"attrs\":{\"onClick\":\"document.querySelector('.u-popup-container').style.display = 'none';\"}},\"backgroundColor\":\"#f9fafb\",\"preheaderText\":\"\",\"linkStyle\":{\"body\":true,\"linkColor\":\"#1a1a1a\",\"linkHoverColor\":\"#0000ee\",\"linkUnderline\":false,\"linkHoverUnderline\":true,\"inherit\":false},\"backgroundImage\":{\"url\":\"\",\"fullWidth\":true,\"repeat\":\"no-repeat\",\"size\":\"custom\",\"position\":\"top-center\",\"customPosition\":[\"50%\",\"0%\"]},\"_meta\":{\"htmlID\":\"u_body\",\"htmlClassNames\":\"u_body\"}}},\"schemaVersion\":16}"

  @create_email """
  mutation CreateEmail($campaignName: String!, $mediaId: ID, $subject: String) {
    createEmail(
      campaignName: $campaignName
      mediaId: $mediaId
      subject: $subject
    ) {
      id
    }
  }
  """

  @duplicate_email """
  mutation DuplicateEmailAndEmailRecipients($id: ID!) {
    duplicateEmailAndEmailRecipients(id: $id) {
      id
    }
  }
  """

  @send_email """
  mutation SendEmail($emailId: ID!) {
    sendEmail(emailId: $emailId) {
      id
      sentAt
    }
  }
  """

  @update_email_and_schedule """
  mutation UpdateEmailSchedule($emailId: ID!, $email: EmailInput!) {
    updateEmailSchedule(emailId: $emailId, email: $email) {
      id
    }
  }
  """

  describe "Send email" do
    setup do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      {:ok,
       attrs: %{company_profile_id: company_profile.id},
       company_profile: company_profile,
       company_profile_user: company_profile_user,
       market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker)}
    end

    test "Sent to intended recipients", %{
      attrs: attrs,
      company_profile: company_profile,
      company_profile_user: company_profile_user,
      conn: conn,
      market_listing_key: market_listing_key
    } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      # Build test data
      shareholding(:past, attrs)

      %{contact: contact_1} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_2} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_3} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_4} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_5} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_6} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_7} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_8} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_9} = shareholding(:past_30_days_new, attrs)

      %{contact: contact_10} = shareholding(:past_30_days_upgrader, attrs)
      shareholding(:past_30_days_upgrader, attrs)

      # TAGS TODO: Change this when new tags are released
      tag(contact_2, %{name: "broker"})

      contact_global_unsubscribe(contact_3)
      contact_suppression(contact_4)
      contact_unsubscribe(contact_5, %{scope: :general})

      dynamic_list_1 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 9,
          filters: [%{key: "trading_activity", value: "new,30"}],
          name: "New shareholders in the last 30 days"
        })

      dynamic_list_2 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 1,
          filters: [%{key: "tags", value: "broker"}],
          name: "Tagged as broker"
        })

      # Step 1 - create an email
      campaign_name_1 = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name_1}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      assert not is_nil(email_id)

      email_1 = Gaia.Comms.get_email(email_id)

      assert email_1.campaign_name == campaign_name_1

      # Step 2 - update email metadata
      campaign_name_2 = Faker.Lorem.sentence(4..5)
      subject_2 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_2,
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => subject_2
          },
          "emailId" => email_id
        }
      )

      email_2 = Gaia.Comms.get_email(email_id)

      assert email_2.campaign_name == campaign_name_2
      assert email_2.subject == subject_2

      # Step 3 - update email recipients
      campaign_name_3 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_3,
            "doNotSendToContactIds" => [contact_6.id],
            "doNotSendToDynamicListIds" => [dynamic_list_2.id],
            "sendToAllContacts" => false,
            "sendToContactIds" => [contact_10.id],
            "sendToDynamicListIds" => [dynamic_list_1.id]
          },
          "emailId" => email_id
        }
      )

      email_3 = Gaia.Comms.get_email(email_id)

      assert email_3.campaign_name == campaign_name_3
      assert email_3.do_not_send_to_contact_ids == [contact_6.id]
      assert email_3.do_not_send_to_dynamic_list_ids == [dynamic_list_2.id]
      assert is_nil(email_3.email_html)
      assert is_nil(email_3.email_json)
      assert not email_3.send_to_all_contacts
      assert email_3.send_to_contact_ids == [contact_10.id]
      assert email_3.send_to_dynamic_list_ids == [dynamic_list_1.id]

      # Step 4 - update email content
      campaign_name_4 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_4,
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      email_4 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert is_struct(email_4, Gaia.Comms.Email)
      assert email_4.campaign_name == campaign_name_4
      assert email_4.company_profile_id == company_profile.id
      assert not is_nil(email_4.email_html)
      assert not is_nil(email_4.email_json)
      assert Enum.empty?(email_4.email_recipients)
      assert email_4.is_draft
      assert not email_4.is_welcome_email
      assert email_4.last_updated_by == company_profile_user.id
      assert not is_nil(email_4.media_id)
      assert is_nil(email_4.sent_at)

      # Step 5 - Send the email
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      Oban.drain_queue(queue: :emails, with_recursion: true, with_scheduled: true)

      email_5 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert email_5.company_profile_id == company_profile.id
      assert Enum.count(email_5.email_recipients) == 5
      assert not email_5.is_draft
      assert not is_nil(email_5.sent_at)

      # Confirm that the email was sent to the intended recipients
      assert Enum.all?([contact_1, contact_7, contact_8, contact_9, contact_10], fn %{
                                                                                      id: contact_id
                                                                                    } ->
               email_5.email_recipients
               |> Enum.find(fn recipient -> recipient.contact_id == contact_id end)
               |> Kernel.is_nil()
               |> Kernel.not()
             end)

      # Confirm that all recipients have been sent
      assert Enum.all?(email_5.email_recipients, fn %{sent_at: sent_at} ->
               not is_nil(sent_at)
             end)
    end

    test "Scheduled email is enqueued with the right timestamp", %{
      attrs: attrs,
      company_profile: company_profile,
      company_profile_user: company_profile_user,
      conn: conn,
      market_listing_key: market_listing_key
    } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      # Build test data
      shareholding(:past, attrs)

      %{contact: _contact_1} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_2} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_3} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_4} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_5} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_6} = shareholding(:past_30_days_new, attrs)
      %{contact: _contact_7} = shareholding(:past_30_days_new, attrs)
      %{contact: _contact_8} = shareholding(:past_30_days_new, attrs)
      %{contact: _contact_9} = shareholding(:past_30_days_new, attrs)

      %{contact: contact_10} = shareholding(:past_30_days_upgrader, attrs)
      shareholding(:past_30_days_upgrader, attrs)

      tag(contact_2, %{name: "broker"})

      contact_global_unsubscribe(contact_3)
      contact_suppression(contact_4)
      contact_unsubscribe(contact_5, %{scope: :general})

      dynamic_list_1 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 9,
          filters: [%{key: "trading_activity", value: "new,30"}],
          name: "New shareholders in the last 30 days"
        })

      dynamic_list_2 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 1,
          filters: [%{key: "tags", value: "broker"}],
          name: "Tagged as broker"
        })

      # Step 1 - create an email
      campaign_name_1 = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name_1}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      assert not is_nil(email_id)

      email_1 = Gaia.Comms.get_email(email_id)

      assert email_1.campaign_name == campaign_name_1

      # Step 2 - update email metadata
      campaign_name_2 = Faker.Lorem.sentence(4..5)
      subject_2 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_2,
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => subject_2
          },
          "emailId" => email_id
        }
      )

      email_2 = Gaia.Comms.get_email(email_id)

      assert email_2.campaign_name == campaign_name_2
      assert email_2.subject == subject_2

      # Step 3 - update email recipients
      campaign_name_3 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_3,
            "doNotSendToContactIds" => [contact_6.id],
            "doNotSendToDynamicListIds" => [dynamic_list_2.id],
            "sendToAllContacts" => false,
            "sendToContactIds" => [contact_10.id],
            "sendToDynamicListIds" => [dynamic_list_1.id]
          },
          "emailId" => email_id
        }
      )

      email_3 = Gaia.Comms.get_email(email_id)

      assert email_3.campaign_name == campaign_name_3
      assert email_3.do_not_send_to_contact_ids == [contact_6.id]
      assert email_3.do_not_send_to_dynamic_list_ids == [dynamic_list_2.id]
      assert is_nil(email_3.email_html)
      assert is_nil(email_3.email_json)
      assert not email_3.send_to_all_contacts
      assert email_3.send_to_contact_ids == [contact_10.id]
      assert email_3.send_to_dynamic_list_ids == [dynamic_list_1.id]

      # Step 4 - update email content
      campaign_name_4 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_4,
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      email_4 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert is_struct(email_4, Gaia.Comms.Email)
      assert email_4.campaign_name == campaign_name_4
      assert email_4.company_profile_id == company_profile.id
      assert not is_nil(email_4.email_html)
      assert not is_nil(email_4.email_json)
      assert Enum.empty?(email_4.email_recipients)
      assert email_4.is_draft
      assert not email_4.is_welcome_email
      assert email_4.last_updated_by == company_profile_user.id
      assert not is_nil(email_4.media_id)
      assert is_nil(email_4.sent_at)

      # Step 5 - Schedule the email
      campaign_name_5 = Faker.Lorem.sentence(4..5)
      scheduled_at = NaiveDateTime.add(utc_now(), 15, :day)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_5,
            "scheduledAt" => NaiveDateTime.to_iso8601(scheduled_at)
          },
          "emailId" => email_id
        }
      )

      email_5 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert email_5.campaign_name == campaign_name_5

      # Step 6 - Call send email mutation
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      [scheduled_oban_job] =
        all_enqueued(worker: Gaia.Jobs.SendEmail, args: %{email_id: email_id})

      assert is_struct(scheduled_oban_job, Oban.Job)
      assert Timex.compare(scheduled_oban_job.scheduled_at, scheduled_at, :seconds) == 0
    end
  end

  describe "Duplicate email" do
    setup do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      {:ok,
       attrs: %{company_profile_id: company_profile.id},
       company_profile: company_profile,
       company_profile_user: company_profile_user,
       market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker)}
    end

    test "Duplicate the email", %{
      attrs: attrs,
      company_profile: company_profile,
      company_profile_user: company_profile_user,
      conn: conn,
      market_listing_key: market_listing_key
    } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      # Build test data
      shareholding(:past, attrs)

      %{contact: contact_1} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_2} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_3} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_4} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_5} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_6} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_7} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_8} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_9} = shareholding(:past_30_days_new, attrs)

      %{contact: contact_10} = shareholding(:past_30_days_upgrader, attrs)
      shareholding(:past_30_days_upgrader, attrs)

      tag(contact_2, %{name: "broker"})

      contact_global_unsubscribe(contact_3)
      contact_suppression(contact_4)
      contact_unsubscribe(contact_5, %{scope: :general})

      dynamic_list_1 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 9,
          filters: [%{key: "trading_activity", value: "new,30"}],
          name: "New shareholders in the last 30 days"
        })

      dynamic_list_2 =
        dynamic_list(%{
          company_profile_id: company_profile.id,
          estimated_contacts_size: 1,
          filters: [%{key: "tags", value: "broker"}],
          name: "Tagged as broker"
        })

      # Step 1 - create an email
      campaign_name_1 = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name_1}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      assert not is_nil(email_id)

      email_1 = Gaia.Comms.get_email(email_id)

      assert email_1.campaign_name == campaign_name_1

      # Step 2 - update email metadata
      campaign_name_2 = Faker.Lorem.sentence(4..5)
      subject_2 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_2,
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => subject_2
          },
          "emailId" => email_id
        }
      )

      email_2 = Gaia.Comms.get_email(email_id)

      assert email_2.campaign_name == campaign_name_2
      assert email_2.subject == subject_2

      # Step 3 - update email recipients
      campaign_name_3 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_3,
            "doNotSendToContactIds" => [contact_6.id],
            "doNotSendToDynamicListIds" => [dynamic_list_2.id],
            "sendToAllContacts" => false,
            "sendToContactIds" => [contact_10.id],
            "sendToDynamicListIds" => [dynamic_list_1.id]
          },
          "emailId" => email_id
        }
      )

      email_3 = Gaia.Comms.get_email(email_id)

      assert email_3.campaign_name == campaign_name_3
      assert email_3.do_not_send_to_contact_ids == [contact_6.id]
      assert email_3.do_not_send_to_dynamic_list_ids == [dynamic_list_2.id]
      assert is_nil(email_3.email_html)
      assert is_nil(email_3.email_json)
      assert not email_3.send_to_all_contacts
      assert email_3.send_to_contact_ids == [contact_10.id]
      assert email_3.send_to_dynamic_list_ids == [dynamic_list_1.id]

      # Step 4 - update email content
      campaign_name_4 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_4,
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      email_4 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert is_struct(email_4, Gaia.Comms.Email)
      assert email_4.campaign_name == campaign_name_4
      assert email_4.company_profile_id == company_profile.id
      assert not is_nil(email_4.email_html)
      assert not is_nil(email_4.email_json)
      assert Enum.empty?(email_4.email_recipients)
      assert email_4.is_draft
      assert not email_4.is_welcome_email
      assert email_4.last_updated_by == company_profile_user.id
      assert not is_nil(email_4.media_id)
      assert is_nil(email_4.sent_at)

      # Step 5 - Send the email
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      Oban.drain_queue(queue: :emails, with_recursion: true, with_scheduled: true)

      email_5 = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert email_5.company_profile_id == company_profile.id
      assert Enum.count(email_5.email_recipients) == 5
      assert not email_5.is_draft
      assert not is_nil(email_5.sent_at)

      # Confirm that the email was sent to the intended recipients
      assert Enum.all?([contact_1, contact_7, contact_8, contact_9, contact_10], fn %{
                                                                                      id: contact_id
                                                                                    } ->
               email_5.email_recipients
               |> Enum.find(fn recipient -> recipient.contact_id == contact_id end)
               |> Kernel.is_nil()
               |> Kernel.not()
             end)

      # Confirm that all recipients have been sent
      assert Enum.all?(email_5.email_recipients, fn %{sent_at: sent_at} ->
               not is_nil(sent_at)
             end)

      # Step 6 - Duplicate the email
      duplicated_email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @duplicate_email,
          variables: %{
            "id" => email_id
          }
        )
        |> get_in(["data", "duplicateEmailAndEmailRecipients", "id"])
        |> String.to_integer()

      email_6 =
        duplicated_email_id
        |> Gaia.Comms.get_email()
        |> Gaia.Repo.preload([
          :email_globally_unsubscribed_contacts,
          :email_recipients,
          :email_unsubscribed_contacts,
          :recipients_tracking_events
        ])

      # The comms_email_recipients table shouldn't be populated when duplicating email
      # The duplicated email should be a draft
      # The duplicated email shouldn't already be sent or scheduled to be sent
      assert Enum.empty?(email_6.email_globally_unsubscribed_contacts)
      assert Enum.empty?(email_6.email_recipients)
      assert Enum.empty?(email_6.email_unsubscribed_contacts)
      assert email_6.is_draft
      assert Enum.empty?(email_6.recipients_tracking_events)
      assert is_nil(email_6.scheduled_at)
      assert is_nil(email_6.sent_at)

      # The duplicated email has a new media_id, because of unique_email_per_media_id constraint
      assert email_5.media_id != email_6.media_id

      assert Enum.all?(
               [
                 :campaign_name,
                 :from_email,
                 :from_name,
                 :email_html,
                 :email_json,
                 :subject,
                 :is_welcome_email,
                 :send_to_all_contacts,
                 :send_to_dynamic_list_ids,
                 :do_not_send_to_dynamic_list_ids,
                 :send_to_contact_ids,
                 :do_not_send_to_contact_ids,
                 :company_profile_id
               ],
               &(Map.get(email_6, &1) == Map.get(email_5, &1))
             )

      # Step 7 - update email recipients of the duplicated email
      campaign_name_7 = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name_7,
            "doNotSendToContactIds" => [],
            "doNotSendToDynamicListIds" => [],
            "sendToAllContacts" => false,
            # contact_3 is globally unsubscribed
            "sendToContactIds" => [contact_3.id, contact_6.id],
            "sendToDynamicListIds" => []
          },
          "emailId" => duplicated_email_id
        }
      )

      email_7 =
        duplicated_email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert is_struct(email_7, Gaia.Comms.Email)
      assert email_7.campaign_name == campaign_name_7
      assert email_7.company_profile_id == company_profile.id
      assert not is_nil(email_7.email_html)
      assert not is_nil(email_7.email_json)
      assert Enum.empty?(email_7.email_recipients)
      assert email_7.is_draft
      assert not email_7.is_welcome_email
      assert email_7.last_updated_by == company_profile_user.id
      assert not is_nil(email_7.media_id)
      assert is_nil(email_7.sent_at)

      # Step 8 - Send the duplicated email
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => duplicated_email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      Oban.drain_queue(queue: :emails, with_recursion: true, with_scheduled: true)

      email_8 =
        duplicated_email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert email_8.company_profile_id == company_profile.id
      assert Enum.count(email_8.email_recipients) == 1
      assert not email_8.is_draft
      assert not is_nil(email_8.sent_at)

      # Confirm that the email was sent to the intended recipients
      assert Enum.all?([contact_6], fn %{id: contact_id} ->
               email_8.email_recipients
               |> Enum.find(fn recipient -> recipient.contact_id == contact_id end)
               |> Kernel.is_nil()
               |> Kernel.not()
             end)

      # Confirm that all recipients have been sent
      assert Enum.all?(email_8.email_recipients, fn %{sent_at: sent_at} ->
               not is_nil(sent_at)
             end)
    end
  end

  describe "Send email with null email contacts" do
    setup do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      {:ok,
       company_profile: company_profile,
       company_profile_user: company_profile_user,
       market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker)}
    end

    test "Sent to all recipients if they have email populated", %{
      company_profile: company_profile,
      company_profile_user: company_profile_user,
      conn: conn,
      market_listing_key: market_listing_key
    } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      # Build test data
      contact_1 = contact(%{company_profile_id: company_profile.id})
      contact_2 = contact(%{company_profile_id: company_profile.id})
      contact_3 = contact(%{company_profile_id: company_profile.id})

      {:ok, contact_4} =
        Gaia.Contacts.create_contact(%{
          company_profile_id: company_profile.id,
          lead_identified_at: NaiveDateTime.utc_now(:second),
          contact_source: :registry_import,
          email: nil
        })

      # Step 1 - create an email
      campaign_name_1 = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name_1}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      # Step 2 - update email metadata
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => Faker.Lorem.sentence(4..5),
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => Faker.Lorem.sentence(4..5)
          },
          "emailId" => email_id
        }
      )

      # Step 3 - update email recipients
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => Faker.Lorem.sentence(4..5),
            "doNotSendToContactIds" => [contact_3.id],
            "doNotSendToDynamicListIds" => [],
            "sendToAllContacts" => false,
            "sendToContactIds" => [contact_1.id, contact_2.id, contact_4.id],
            "sendToDynamicListIds" => []
          },
          "emailId" => email_id
        }
      )

      # Step 4 - update email content
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => Faker.Lorem.sentence(4..5),
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      # Step 5 - Send the email
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      Oban.drain_queue(queue: :emails, with_recursion: true, with_scheduled: true)

      email =
        email_id
        |> Gaia.Comms.get_email()
        |> Gaia.Repo.preload([:email_recipients])

      assert email.company_profile_id == company_profile.id
      # contact_1 and contact_2
      assert Enum.count(email.email_recipients) == 2
      assert not email.is_draft
      assert not is_nil(email.sent_at)

      # Confirm that the email was sent to the intended recipients
      assert Enum.all?([contact_1, contact_2], fn %{id: contact_id} ->
               email.email_recipients
               |> Enum.find(fn recipient -> recipient.contact_id == contact_id end)
               |> Kernel.is_nil()
               |> Kernel.not()
             end)

      # Assert contact_4 is not in the recipient list
      assert Enum.find(email.email_recipients, fn recipient ->
               recipient.contact_id == contact_4.id
             end) == nil

      # Confirm that all recipients have been sent
      assert Enum.all?(email.email_recipients, fn %{sent_at: sent_at} ->
               not is_nil(sent_at)
             end)
    end
  end

  describe "Send email with static list " do
    setup do
      company_profile = company_profile()
      company_profile_user = company_profile_user(%{profile_id: company_profile.id})

      {:ok,
       attrs: %{company_profile_id: company_profile.id},
       company_profile: company_profile,
       company_profile_user: company_profile_user,
       market_listing_key: Gaia.Markets.Ticker.resolve_market_listing_key(company_profile.ticker)}
    end

    test "Static list members from static lists become recipients", %{
      attrs: attrs,
      company_profile: company_profile,
      company_profile_user: company_profile_user,
      conn: conn,
      market_listing_key: market_listing_key
    } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      # Create static list and insert contacts
      %{contact: contact_1} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_2} = shareholding(:past_30_days_new, attrs)

      static_list = static_list(%{company_profile_id: company_profile.id, name: "Static List 1"})

      static_list_member(%{contact_id: contact_1.id, static_list_id: static_list.id})
      static_list_member(%{contact_id: contact_2.id, static_list_id: static_list.id})

      # Step 1 - Create an email
      campaign_name = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      assert not is_nil(email_id)

      subject_2 = Faker.Lorem.sentence(4..5)

      # Step 2 - Update email recipients with static list
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name,
            "sendToStaticListIds" => [static_list.id],
            "doNotSendToStaticListIds" => [],
            "doNotSendToContactIds" => [],
            "doNotSendToDynamicListIds" => [],
            "sendToAllContacts" => false,
            "sendToDynamicListIds" => [],
            "sendToContactIds" => [],
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => subject_2,
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      email = Gaia.Comms.get_email(email_id)

      assert email.send_to_static_list_ids == [static_list.id]
      email = Gaia.Repo.preload(email, [:email_recipients])

      # Step 3 - Email should not have recipients yet
      assert Enum.empty?(email.email_recipients)

      # Step 4 - Send email and ensure members from the static list are used as recipients
      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      email = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert not Enum.empty?(email.email_recipients) and Enum.count(email.email_recipients) == 2
    end

    test "Exclude static list members from being email recipients if their static list IDs are included in do_not_include_static_list_ids",
         %{
           attrs: attrs,
           company_profile: company_profile,
           company_profile_user: company_profile_user,
           conn: conn,
           market_listing_key: market_listing_key
         } do
      authenticated_conn = authenticate_company_user(conn, company_profile_user.user)

      %{contact: contact_1} = shareholding(:past_30_days_new, attrs)
      %{contact: contact_2} = shareholding(:past_30_days_new, attrs)

      static_list = static_list(%{company_profile_id: company_profile.id, name: "Static List 1"})

      static_list_member(%{contact_id: contact_1.id, static_list_id: static_list.id})
      static_list_member(%{contact_id: contact_2.id, static_list_id: static_list.id})

      campaign_name = Faker.Lorem.sentence(4..5)

      email_id =
        authenticated_conn
        |> graphql_query(
          market_listing_key: market_listing_key,
          query: @create_email,
          variables: %{"campaignName" => campaign_name}
        )
        |> get_in(["data", "createEmail", "id"])
        |> String.to_integer()

      assert not is_nil(email_id)

      subject = Faker.Lorem.sentence(4..5)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @update_email_and_schedule,
        variables: %{
          "email" => %{
            "campaignName" => campaign_name,
            "sendToStaticListIds" => [static_list.id],
            "doNotSendToStaticListIds" => [static_list.id],
            "doNotSendToContactIds" => [],
            "doNotSendToDynamicListIds" => [],
            "sendToAllContacts" => false,
            "sendToDynamicListIds" => [],
            "sendToContactIds" => [],
            "fromEmail" => "no-reply@#{company_profile.custom_domain.custom_domain}",
            "fromName" => "Investor Relations",
            "subject" => subject,
            "emailHtml" => @email_html,
            "emailJson" => @email_json
          },
          "emailId" => email_id
        }
      )

      email = Gaia.Comms.get_email(email_id)

      assert email.send_to_static_list_ids == [static_list.id]
      assert email.do_not_send_to_static_list_ids == [static_list.id]
      email = Gaia.Repo.preload(email, [:email_recipients])

      assert Enum.empty?(email.email_recipients)

      graphql_query(authenticated_conn,
        market_listing_key: market_listing_key,
        query: @send_email,
        variables: %{"emailId" => email_id}
      )

      Oban.drain_queue(queue: :send_email, with_recursion: true, with_scheduled: true)

      email = email_id |> Gaia.Comms.get_email() |> Gaia.Repo.preload([:email_recipients])

      assert Enum.empty?(email.email_recipients)
    end
  end
end
